import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../controllers/pets.controller";
import { PetModule } from "../modules/pets/pets.module";
import { OwnerController } from "../controllers/owners.controller";
import { OwnerModule } from "../modules/owners/owners.module";
import { Visit<PERSON>ontroller } from "../controllers/visits.controller";
import { VisitModule } from "../modules/visits/visits.module";
import { <PERSON>d<PERSON>ontroller } from "../controllers/breeds.controller";
import { BreedModule } from "../modules/breeds/breeds.module";
import { QuestionaireController } from "../controllers/questionaires.controller";
import { QuestionaireModule } from "../modules/questionaires/questionaires.module";
import { RequirementController } from "../controllers/requirements.controller";
import { RequirementModule } from "../modules/requirements/requirements.module";
import { RecipeController } from "../controllers/recipes.controller";
import { RecipeModule } from "../modules/recipes/recipes.module";
import { OrderController } from "../controllers/orders.controller";
import { OrderModule } from "../modules/orders/orders.module";
import { UserController } from "../controllers/users.controller";
import { UserModule } from "../modules/users/users.module";
import { RoleController } from "../controllers/roles.controller";
import { RoleModule } from "../modules/roles/roles.module";
import { PermissionController } from "../controllers/permissions.controller";
import { PermissionModule } from "../modules/permissions/permissions.module";
import { DoctorController } from "../controllers/doctors.controller";
import { DoctorModule } from "../modules/doctors/doctors.module";
import { DiseaseController } from "../controllers/diseases.controller";
import { DiseaseModule } from "../modules/diseases/disease.module";

import { verifyToken } from "../middlewares/verifyToken";

import express from "express";

const router = express.Router();

const petModule = new PetModule();
const petController = new PetController(petModule);

const ownerModule = new OwnerModule();
const ownerController = new OwnerController(ownerModule);

const visitModule = new VisitModule();
const visitController = new VisitController(visitModule);

const breedModule = new BreedModule();
const breedController = new BreedController(breedModule);

const questionaireModule = new QuestionaireModule();
const questionaireController = new QuestionaireController(questionaireModule);

const requirementModule = new RequirementModule();
const requirementController = new RequirementController(requirementModule);

const recipeModule = new RecipeModule();
const recipeController = new RecipeController(recipeModule);

const orderModule = new OrderModule();
const orderController = new OrderController(orderModule);

const userModule = new UserModule();
const userController = new UserController(userModule);

const roleModule = new RoleModule();
const roleController = new RoleController(roleModule);

const permissionModule = new PermissionModule();
const permissionController = new PermissionController(permissionModule);

const doctorModule = new DoctorModule();
const doctorController = new DoctorController(doctorModule);

const diseaseModule = new DiseaseModule();
const diseaseController = new DiseaseController(diseaseModule);

router.use(verifyToken);
router.use("/pet", petController.router);
router.use("/owner", ownerController.router);
router.use("/visit", visitController.router);
router.use("/breed", breedController.router);
router.use("/questionaire", questionaireController.router);
router.use("/requirement", requirementController.router);
router.use("/recipe", recipeController.router);
router.use("/order", orderController.router);
router.use("/user", userController.router);
router.use("/role", roleController.router);
router.use("/permission", permissionController.router);
router.use("/doctor", doctorController.router);
router.use("/disease", diseaseController.router);
export default router;
