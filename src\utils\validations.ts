import {
    z,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    Zod<PERSON>rror,
    ZodString,
    ZodNumber,
    ZodBoolean,
    ZodObject,
    ZodDate,
    ZodIssue,
    ZodAny,
    ZodArray,
} from "zod";
import { ResponseValidationTypeEnum as v, ResponseValidationMessageEnum as m } from "../enum/validate";
import { FieldConfigType } from "../types/validate.type";
import { NutrientTypeEnum } from "../enum/requirement";
import {
    PetGenderTypeEnum,
    PetSpeciesEnum,
    PetLivingEnvironmentEnum,
    ActiveScoreCalculationMethodEnum,
    ActiveScoreVisualLevelEnum,
} from "../enum/pet";
import { MappingAlgorithmEnum, PetAllergicEnum } from "../enum/visit";

class Validator {
    private readonly fieldName: string;
    private readonly config: FieldConfigType;

    constructor(fieldName: string, config: FieldConfigType) {
        this.fieldName = fieldName;
        this.config = config;
    }

    public getValidator(): ZodTypeAny {
        const { type, enumValues = [] } = this.config;

        // Object lookup instead of switch case
        const validatorMap: Record<string, () => ZodTypeAny> = {
            [v.UUID]: () => this.validateUUID(),
            [v.STRING]: () => this.validateRequiredString(),
            [v.NUMBER]: () => this.validateRequiredNumber(),
            [v.BOOLEAN]: () => this.validateRequiredBoolean(),
            [v.OBJECT]: () => this.validateObject(),
            [v.ENUM]: () => this.validateEnum(enumValues),
            [v.DATE]: () => this.validateDate(),
            [v.EMAIL]: () => this.validateEmail(),
            [v.PHONE]: () => this.validatePhone(),
            [v.LETTER_ONLY]: () => this.validateLettersOnly(),
            [v.NO_SPECIAL_CHARACTERS]: () => this.validateNoSpecialChars(),
            [v.NO_PROHIBITED_CHARACTERS]: () => this.validateNoProhibitedChars(),
            [v.KEYCLOAK_PROHIBITED_CHARACTERS]: () => this.validateKeycloakProhibitedChars(),
            [v.PREFIX]: () => this.validatePrefix(),
            // Custom Object Schemas เดิม
            [v.RECIPE_DETAILS]: () => this.validateRecipeDetails(),
            [v.QUESTIONARE]: () => this.validateQuestionares(),
            [v.NUTRIENT_RECIPE]: () => this.validateNutrientsRecipe(),
            [v.NUTRIENT]: () => this.validateNutrients(),
            [v.PERSONAL_REQUIREMENTS]: () => this.validatePersonalRequirements(),
            [v.EXTRA_MEALS]: () => this.validateExtraMeals(),
            [v.PET_INFO]: () => this.validatePetInfo(),
            [v.OWNER_INFO]: () => this.validateOwnerInfo(),
            [v.DOCTOR_INFO]: () => this.validateDoctorInfo(),
            [v.PET_HEALTH]: () => this.validatePetHealth(),
            [v.PET_REQUIREMENT]: () => this.validatePetRequirement(),
            [v.PET_NUTRITION]: () => this.validatePetNutrition(),
            [v.PET_PRODUCT_MATCH]: () => this.validatePetProductMatch(),
            [v.WEIGHTS]: () => this.validateWeights(),
        };
        // Return the validator or default if not found
        const validatorFn = validatorMap[type];
        const baseSchema = validatorFn ? validatorFn() : z.string().min(1, "none type");
        return this.wrapOptionality(baseSchema);
    }

    private wrapOptionality(baseSchema: ZodTypeAny): ZodTypeAny {
        const { allowNull = false, required = false, array = false, min, max, minLength, maxLength } = this.config;

        if (baseSchema instanceof ZodString) {
            let zodConfig: ZodString = baseSchema;
            if (typeof minLength === "number") zodConfig = zodConfig.min(minLength, m.MIN_CHAR);
            if (typeof maxLength === "number") zodConfig = zodConfig.max(maxLength, m.MAX_CHAR);
            baseSchema = zodConfig;
        }
        if (baseSchema instanceof ZodNumber) {
            let zodConfig: ZodNumber = baseSchema;
            if (typeof min === "number") zodConfig = zodConfig.min(min, m.MIN_NUMBER);
            if (typeof max === "number") zodConfig = zodConfig.max(max, m.MAX_NUMBER);
            baseSchema = zodConfig;
        }

        if (array && !baseSchema._def.typeName.endsWith("Array")) baseSchema = baseSchema.array();
        if (!required) baseSchema = baseSchema.optional();
        if (allowNull) baseSchema = baseSchema.nullable();

        return baseSchema;
    }
    //Required
    private validateRequiredString(): ZodString {
        let base = z.string({ invalid_type_error: m.STRING, required_error: m.REQUIRED });
        return base;
    }
    private validateRequiredNumber(): ZodNumber {
        let base = z.number({ invalid_type_error: m.NUMBER, required_error: m.REQUIRED });
        return base;
    }
    private validateRequiredBoolean(): ZodBoolean {
        let base = z.boolean({ invalid_type_error: m.BOOLEAN, required_error: m.REQUIRED });
        return base;
    }

    private validateRequiredDate(): z.ZodEffects<z.ZodEffects<z.ZodDate, Date, Date>, Date, unknown> {
        let base = z.preprocess(
            (str) => {
                if (str === undefined) return undefined;
                if (typeof str == "object") return str;
                const temp = String(str);
                const isoDateRegex =
                    /^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(\.\d{3})?Z?$/;
                if (!isoDateRegex.test(temp)) return "";
                const date = new Date(temp);
                return date;
            },
            z.date({ required_error: m.REQUIRED, invalid_type_error: m.DATE }).refine(
                (str) => {
                    return !isNaN(str.getTime());
                },
                {
                    message: m.DATE,
                }
            )
        );
        return base;
    }
    //Enum
    private validateEnum(enumValues: string[]): z.ZodEffects<z.ZodString, string, string> {
        let base = this.validateRequiredString().refine((val) => enumValues.includes(val), {
            message: m.ENUM,
        });
        return base;
    }
    //Email
    private validateEmail(): ZodString {
        let base = this.validateRequiredString().email(m.EMAIL);
        return base;
    }
    //UUID
    private validateUUID(): ZodString {
        // UUID validation
        // This regex checks for both v4 and v7 UUIDs
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[47][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        let base = this.validateRequiredString().regex(uuidRegex, m.UUID);
        return base;
    }

    //Phone
    private validatePhone(): ZodString {
        const phoneRegex = /^(08|09|06)\d{8}$|^(02|03|04|05|07)\d{7}$/;
        let base = this.validateRequiredString().regex(phoneRegex, m.PHONE);
        return base;
    }

    //Letters
    private validateLettersOnly(): ZodString {
        const pattern = /^[a-zA-Z\u0E00-\u0E7F\s]*$/;
        let base = this.validateRequiredString().regex(pattern, m.LETTER_ONLY);
        return base;
    }

    //No Special
    private validateNoSpecialChars(): ZodString {
        const pattern = /^[a-zA-Z0-9\u0E00-\u0E7F\s]*$/;
        let base = this.validateRequiredString().regex(pattern, m.NO_SPECIAL_CHARACTERS);
        return base;
    }

    //No Prohibited characters
    private validateNoProhibitedChars(): ZodString {
        const pattern = /^[a-zA-Z0-9\u0E00-\u0E7F.,_() '/-]*$/; // This regex allows letters, numbers, Thai characters, and the characters . , _ () -'/ and space
        let base = this.validateRequiredString().regex(pattern, m.NO_PROHIBITED_CHARACTERS);
        return base;
    }
    //No Prohibited characters for keycloak
    private validateKeycloakProhibitedChars(): ZodString {
        const pattern = /^[a-zA-Z0-9\u0E00-\u0E7F._-]*$/;
        let base = this.validateRequiredString().regex(pattern, m.NO_PROHIBITED_CHARACTERS);
        return base;
    }

    // Date
    private validateDate(): ZodTypeAny {
        return this.validateRequiredDate();
    }

    // Array
    private validateArray(value: ZodTypeAny): ZodArray<ZodTypeAny> {
        let base = z.array(value, { invalid_type_error: m.ARRAY, required_error: m.REQUIRED });
        return base;
    }

    // Object
    private validateObject(): ZodObject<Record<string, ZodTypeAny>> {
        if (this.config.objectConfig) {
            return ValidationService.createSchema(this.config.objectConfig);
        }
        console.warn(`Field '${this.fieldName}' has type '${v.OBJECT}' but no 'objectConfig' was provided. `);
        return z.object({});
    }

    // ------------- Custom Object -------------

    // RECIPE_DETAILS
    private validateRecipeDetails(): ZodTypeAny {
        return this.validateArray(
            z.object({
                recipeName: this.validateRequiredString(),
                matchPercentage: this.validateRequiredNumber().min(0).max(100),
                recipeNutrients: this.validateArray(
                    z.object({
                        nutrientName: this.validateRequiredString(),
                        unit: this.validateRequiredString(),
                        type: this.validateEnum(Object.values(NutrientTypeEnum)),
                        min: this.validateRequiredNumber().min(0),
                        max: this.validateRequiredNumber().min(0).max(999999),
                        isInRange: this.validateRequiredBoolean(),
                    })
                ).min(1, m.MIN_ARRAY_ELEMENT),
                additives: z
                    .array(
                        z.object({
                            rawMatName: this.validateRequiredString().min(1, m.MIN_CHAR),
                            rawMatAmount: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
                            rawMatNutrients: z
                                .array(
                                    z.object({
                                        nutrientName: this.validateRequiredString().min(1, m.MIN_CHAR),
                                        unit: this.validateRequiredString().min(1, m.MIN_CHAR),
                                        type: this.validateEnum(Object.values(NutrientTypeEnum)),
                                        value: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
                                    })
                                )
                                .min(1),
                        })
                    )
                    .nullable(),
                recipeDetails: z.object({
                    me: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
                    dailyAmount: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
                    mealsPerDay: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
                    amountPerMeal: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
                    batchMakingFor: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
                }),
            })
        ).min(1, m.MIN_ARRAY_ELEMENT);
    }

    // QUESTIONAIRE
    private validateQuestionares(): ZodTypeAny {
        return z
            .array(
                z.object({
                    title: this.validateRequiredString().min(1, m.MIN_CHAR),
                    section: this.validateRequiredString().min(1, m.MIN_CHAR),
                    order: this.validateRequiredNumber().int().positive(),
                    questions: z
                        .array(
                            z.object({
                                index: this.validateRequiredNumber().int().positive(),
                                text: this.validateRequiredString().min(1, m.MIN_CHAR),
                                score: this.validateRequiredNumber().min(0, m.MIN_NUMBER).max(1, m.MAX_NUMBER),
                                isSelected: this.validateRequiredBoolean(),
                            })
                        )
                        .min(1),
                })
            )
            .min(1);
    }

    // NUTRIENT_RECIPE
    private validateNutrientsRecipe(): ZodTypeAny {
        return z
            .array(
                z.object({
                    nutrientName: this.validateRequiredString().min(1, m.MIN_CHAR),
                    unit: this.validateRequiredString().min(1, m.MIN_CHAR),
                    type: this.validateEnum(Object.values(NutrientTypeEnum)),
                    value: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
                })
            )
            .min(1);
    }

    // NUTRIENT
    private validateNutrients(): ZodTypeAny {
        return z
            .array(
                z.object({
                    nutrientName: this.validateRequiredString(),
                    unit: this.validateRequiredString(),
                    type: this.validateEnum(Object.values(NutrientTypeEnum)),
                    min: this.validateRequiredNumber().min(0),
                    max: this.validateRequiredNumber().min(0).max(999999),
                })
            )
            .min(1);
    }

    // PERSONAL_REQUIREMENTS
    private validatePersonalRequirements(): ZodTypeAny {
        return z.object({
            mappingMethod: this.validateEnum(Object.values(MappingAlgorithmEnum)),
            requirements: z.array(this.validateRequiredString().min(1, m.MIN_CHAR)).min(1),
        });
    }

    // EXTRA_MEALS
    private validateExtraMeals(): ZodTypeAny {
        return z
            .array(
                z.object({
                    foodName: this.validateRequiredString().min(1, m.MIN_CHAR),
                    brand: this.validateRequiredString().min(1, m.MIN_CHAR),
                    dailyAmount: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
                    nutrients: z.object({
                        protein: this.validateRequiredNumber().nullable(),
                        fat: this.validateRequiredNumber().nullable(),
                        sodium: this.validateRequiredNumber().nullable(),
                        sugars: this.validateRequiredNumber().nullable(),
                        energy: this.validateRequiredNumber().nullable(),
                        water: this.validateRequiredNumber().nullable(),
                    }),
                })
            )
            .min(1);
    }

    // PET_INFO
    private validatePetInfo(): ZodTypeAny {
        return z.object({
            hn: this.validateRequiredString().min(1, m.MIN_CHAR),
            name: this.validateRequiredString().min(1, m.MIN_CHAR),
            gender: this.validateEnum(Object.values(PetGenderTypeEnum)),
            species: this.validateEnum(Object.values(PetSpeciesEnum)),
            breed: this.validateRequiredString().min(1, m.MIN_CHAR),
            isMixedBreed: this.validateRequiredBoolean(),
            birthDate: this.validateDate().nullable(),
            weights: z
                .array(
                    z.object({
                        weight: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
                        measuredDate: this.validateDate(),
                    })
                )
                .min(1),
            livingEnvironment: this.validateEnum(Object.values(PetLivingEnvironmentEnum)),
            isNeutering: this.validateRequiredBoolean(),
            diseases: z.array(
                z.object({
                    diseaseId: this.validateRequiredString().min(1, m.MIN_CHAR),
                    diseaseName: this.validateRequiredString().min(1, m.MIN_CHAR),
                    validFrom: this.validateDate(),
                    validTo: this.validateDate(),
                    createdAt: this.validateDate(),
                    updatedAt: this.validateDate(),
                })
            ),
            activeScore: z.object({
                score: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
                calculationMethod: this.validateEnum(Object.values(ActiveScoreCalculationMethodEnum)),
                questionaires: this.validateArray(
                    z.object({
                        title: this.validateRequiredString().min(1, m.MIN_CHAR),
                        section: this.validateRequiredString().min(1, m.MIN_CHAR),
                        order: this.validateRequiredNumber().int().positive(),
                        questions: z
                            .array(
                                z.object({
                                    index: this.validateRequiredNumber().int().positive(),
                                    text: this.validateRequiredString().min(1, m.MIN_CHAR),
                                    score: this.validateRequiredNumber().min(0, m.MIN_NUMBER).max(1, m.MAX_NUMBER),
                                    isSelected: this.validateRequiredBoolean(),
                                })
                            )
                            .min(1),
                    })
                )
                    .min(1, m.MIN_ARRAY_ELEMENT)
                    .nullable(),
                visualLevel: this.validateEnum(Object.values(ActiveScoreVisualLevelEnum)).nullable(),
            }),
        });
    }

    // OWNER_INFO
    private validateOwnerInfo(): ZodTypeAny {
        return z.object({
            firstName: this.validateRequiredString().min(1, m.MIN_CHAR),
            lastName: this.validateRequiredString().min(1, m.MIN_CHAR),
            phoneNumber: this.validatePhone(),
        });
    }

    // DOCTOR_INFO
    private validateDoctorInfo(): ZodTypeAny {
        return z.object({
            prefix: this.validateRequiredString().min(1, m.MIN_CHAR),
            firstName: this.validateRequiredString().min(1, m.MIN_CHAR),
            lastName: this.validateRequiredString().min(1, m.MIN_CHAR),
            phoneNumber: this.validatePhone(),
            email: this.validateEmail().nullable(),
        });
    }

    // PET_HEALTH
    private validatePetHealth(): ZodTypeAny {
        return z.object({
            currentBcs: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
            idealBcs: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
            currentWeight: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
            idealWeight: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
            questionaires: z
                .array(
                    z.object({
                        title: this.validateRequiredString().min(1, m.MIN_CHAR),
                        section: this.validateRequiredString().min(1, m.MIN_CHAR),
                        order: this.validateRequiredNumber().int().positive(),
                        questions: z
                            .array(
                                z.object({
                                    index: this.validateRequiredNumber().int().positive(),
                                    text: this.validateRequiredString().min(1, m.MIN_CHAR),
                                    score: this.validateRequiredNumber().min(0, m.MIN_NUMBER).max(1, m.MAX_NUMBER),
                                    isSelected: this.validateRequiredBoolean(),
                                })
                            )
                            .min(1),
                    })
                )
                .min(1),
        });
    }

    // PET_REQUIREMENT
    private validatePetRequirement(): ZodTypeAny {
        return z.object({
            diseases: z.array(
                z.object({
                    diseaseId: this.validateRequiredString().min(1, m.MIN_CHAR),
                    diseaseName: this.validateRequiredString().min(1, m.MIN_CHAR),
                    validFrom: this.validateDate(),
                    validTo: this.validateDate(),
                    createdAt: this.validateDate(),
                    updatedAt: this.validateDate(),
                })
            ),
            requirementsInfo: z.object({
                mappingMethod: this.validateEnum(Object.values(MappingAlgorithmEnum)),
                profileRef: this.validateRequiredString().min(1, m.MIN_CHAR),
                requirements: z.array(this.validateRequiredString().min(1, m.MIN_CHAR)).min(1),
            }),
            nutritionNeeds: this.validateRequiredString().min(1, m.MIN_CHAR),
            foodsHistory: z.object({
                extraMeals: z.array(
                    z.object({
                        foodName: this.validateRequiredString().min(1, m.MIN_CHAR),
                        brand: this.validateRequiredString().min(1, m.MIN_CHAR),
                        dailyAmount: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
                        compositions: z
                            .object({
                                protein: this.validateRequiredNumber().nullable(),
                                fat: this.validateRequiredNumber().nullable(),
                                fiber: this.validateRequiredNumber().nullable(),
                                moisture: this.validateRequiredNumber().nullable(),
                                sodium: this.validateRequiredNumber().nullable(),
                                ash: this.validateRequiredNumber().nullable(),
                                me: this.validateRequiredNumber().nullable(),
                            })
                            .nullable(),
                    })
                ),
                allergic: z.array(this.validateEnum(Object.values(PetAllergicEnum))),
                foodsToAvoid: z.array(this.validateEnum(Object.values(PetAllergicEnum))),
            }),
        });
    }

    // PET_NUTRITION
    private validatePetNutrition(): ZodTypeAny {
        return z.object({
            personalNutrients: z
                .array(
                    z.object({
                        nutrientName: this.validateRequiredString().min(1, m.MIN_CHAR),
                        unit: this.validateRequiredString().min(1, m.MIN_CHAR),
                        type: this.validateEnum(Object.values(NutrientTypeEnum)),
                        min: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
                        max: this.validateRequiredNumber().min(0, m.MIN_NUMBER).max(999999),
                    })
                )
                .min(1),
            energyRequirements: z.object({
                stomachSize: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
                weightTypeSelected: this.validateRequiredString().min(1, m.MIN_CHAR),
                factor: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
                rer: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
                der: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
            }),
        });
    }

    // PET_PRODUCT_MATCH
    private validatePetProductMatch(): ZodTypeAny {
        return z.object({
            matchingRecipes: z
                .array(
                    z.object({
                        recipeName: this.validateRequiredString().min(1, m.MIN_CHAR),
                        matchPercentage: this.validateRequiredNumber().min(0, m.MIN_NUMBER).max(100),
                        recipeNutrients: z
                            .array(
                                z.object({
                                    nutrientName: this.validateRequiredString().min(1, m.MIN_CHAR),
                                    unit: this.validateRequiredString().min(1, m.MIN_CHAR),
                                    type: this.validateEnum(Object.values(NutrientTypeEnum)),
                                    min: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
                                    max: this.validateRequiredNumber().min(0, m.MIN_NUMBER).max(999999),
                                    isInRange: this.validateRequiredBoolean(),
                                })
                            )
                            .min(1),
                        additives: z
                            .array(
                                z.object({
                                    rawMatName: this.validateRequiredString().min(1, m.MIN_CHAR),
                                    rawMatAmount: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
                                    rawMatNutrients: z
                                        .array(
                                            z.object({
                                                nutrientName: this.validateRequiredString().min(1, m.MIN_CHAR),
                                                unit: this.validateRequiredString().min(1, m.MIN_CHAR),
                                                type: this.validateEnum(Object.values(NutrientTypeEnum)),
                                                value: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
                                            })
                                        )
                                        .min(1),
                                })
                            )
                            .nullable(),
                        recipeDetails: z.object({
                            me: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
                            dailyAmount: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
                            mealsPerDay: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
                            amountPerMeal: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
                            batchMakingFor: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
                        }),
                    })
                )
                .min(1),
        });
    }

    // WEIGHTS
    private validateWeights(): ZodTypeAny {
        return z
            .array(
                z.object({
                    weight: this.validateRequiredNumber().min(0, m.MIN_NUMBER),
                    measuredDate: this.validateDate(),
                })
            )
            .min(1);
    }

    // PREFIX
    private validatePrefix(): ZodString {
        const pattern = /^[a-zA-Z\u0E00-\u0E7F.\s]*$/;
        let base = z.string({ required_error: m.REQUIRED }).regex(pattern, m.PREFIX);
        return base;
    }
}

type ZodFormattedError<T> = {
    [K in keyof T]?: T[K] extends (infer U)[]
        ? U extends object
            ? Array<{ _index: number } & ZodFormattedError<U>>
            : Array<{ _index: number } & { [key: string]: any }>
        : T[K] extends object
        ? ZodFormattedError<T[K]>
        : string | { [key: string]: any };
};

class ValidationService {
    public static createSchema(fields: Record<string, FieldConfigType>): ZodObject<Record<string, ZodTypeAny>> {
        const schemaObject = Object.entries(fields).reduce((acc, [field, config]) => {
            const validator = new Validator(field, config);
            acc[field] = validator.getValidator();
            return acc;
        }, {} as Record<string, ZodTypeAny>);

        return z.object(schemaObject);
    }

    private static buildNestedError<T>(
        formattedErrors: ZodFormattedError<T>,
        path: (string | number)[],
        errorObject: any
    ) {
        if (path.length === 0) return;

        const [key, ...restOfPath] = path;
        const nextIsNumber = typeof restOfPath[0] === "number";

        if (nextIsNumber) {
            const fieldKey = key as keyof T;
            const arr = (formattedErrors[fieldKey] ?? []) as any[];

            const index = restOfPath[0] as number;
            let existingItem = arr.find((item) => item._index === index);

            if (!existingItem) {
                existingItem = { _index: index };
                arr.push(existingItem);
            }

            formattedErrors[fieldKey] = arr as any;

            if (restOfPath.length === 1) {
                if (typeof errorObject === "string") {
                    existingItem["_error"] = errorObject;
                } else {
                    Object.assign(existingItem, errorObject);
                }
            } else {
                this.buildNestedError(existingItem, restOfPath.slice(1), errorObject);
            }
        } else {
            const fieldKey = key as keyof T;
            if (restOfPath.length === 0) {
                (formattedErrors as any)[fieldKey] = errorObject;
            } else {
                formattedErrors[fieldKey] ??= {} as any;
                this.buildNestedError(formattedErrors[fieldKey] as ZodFormattedError<any>, restOfPath, errorObject);
            }
        }
    }
    /**
     * Validate data against a set of field configurations.
     * @template U - The type of the field configurations.
     * @template T - The type of the data to validate.
     * @param {U} fields - The field configurations.
     * @param {T} data - The data to validate.
     * @returns {Promise<{ isValid: boolean; data: T | Record<string, any>; errors: Record<string, any> | null }>} - The validation result.
     * @example
     * const fields = {
     *     name: { type: v.NO_PROHIBITED_CHARACTERS, required: true, minLength: 2, maxLength: 80 },
     * };
     * const data = {
     *     name: "Disease",
     * };
     * const result = ValidationService.validate(fields, data);
     * @example
     * Error Response
     *     invalidValue: { name: "InvalidProhibitedCharacters" },
     *
     *     minLength: { name: { _error: "InvalidMinimumCharacters", _min: 2 } },
     *
     *     maxLength: { name: { _error: "InvalidMaximumCharacters", _max: 80 } },
     *
     *     required: { name: "Required" },
     */
    public static validate<
        U extends Record<string, FieldConfigType>,
        T extends Record<string, string | number | boolean | Date | string[] | object[] | object | null>
    >(fields: U, data: T) {
        const schema = this.createSchema(fields);
        try {
            const validatedData = schema.parse(data);
            return { isValid: true, data: validatedData, errors: null };
        } catch (err) {
            if (err instanceof ZodError) {
                const formattedErrors: ZodFormattedError<T> = {};
                err.errors.forEach((e: ZodIssue) => {
                    let errorObject: any = e.message;

                    if (e.code === "too_small" || e.code === "too_big") {
                        errorObject = { _error: e.message };
                        if (e.code === "too_small" && e.minimum !== undefined) {
                            errorObject._min = e.minimum;
                        }
                        if (e.code === "too_big" && e.maximum !== undefined) {
                            errorObject._max = e.maximum;
                        }
                    }

                    this.buildNestedError(formattedErrors, e.path, errorObject);
                });

                return { isValid: false, data: formattedErrors, errors: null };
            }
            throw err;
        }
    }
}

// Export the main validation function
export const validated = <
    U extends Record<string, FieldConfigType>,
    T extends Record<string, string | number | boolean | Date | string[] | object[] | object | null>
>(
    fields: U,
    data: T
) => {
    return ValidationService.validate(fields, data);
};
