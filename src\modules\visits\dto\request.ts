import { VisitStatusEnum, VisitStepEnum } from "../../../enum/visit";
import {
    PetHealthSubmitType,
    PetRequirementSubmitType,
    PetNutritionSubmitType,
    PetProductMatchSubmitType
} from "../../../entities/visits.entity";
import { PetInfoRequest } from "../../../modules/pets/dto/request";
import { MatchingRecipes } from "../../../entities/orders.entity";
import { DoctorInfo } from "../../../entities/doctors.entity";
import { OwnerInfo } from "../../../entities/owners.entity";

export type VisitRequest = {
    petId: string;
    doctorId: string;
    visitStep: VisitStepEnum;
    status: VisitStatusEnum;
    petInfo: PetInfoRequest;
    doctorInfo: DoctorInfo;
    petHealth: PetHealthSubmitType | null;
    petRequirement: PetRequirementSubmitType | null;
    petNutrition: PetNutritionSubmitType | null;
    petProductMatch: PetProductMatchSubmitType | null;
};

export type CreateVisitFromPetRequest = {
    ownerId: string | null,
    doctorId: string,
    visitStep: VisitStepEnum;
    status: VisitStatusEnum;
    petInfo: PetInfoRequest;
    ownerInfo: OwnerInfo | null;
    doctorInfo: DoctorInfo;
    petHealth: PetHealthSubmitType | null;
    petRequirement: PetRequirementSubmitType | null;
    petNutrition: PetNutritionSubmitType | null;
    petProductMatch: PetProductMatchSubmitType | null;
}

export type PetInfoSubmitRequest = {
    visitId: string;
};

export type PetHealthSubmitRequest = {
    visitId: string;
    petHealth: PetHealthSubmitType;
};

export type PetRequirementSubmitRequest = {
    visitId: string;
    petId: string;
    petRequirement: PetRequirementSubmitType;
}

export type PetNutritionSubmitRequest = {
    visitId: string;
    petNutrition: PetNutritionSubmitType;
}

export type PetProductMatchSubmitRequest = {
    visitId: string;
    petProductMatch: PetProductMatchSubmitType;
}

export type OrderSummarySubmitRequest = {
    visitId: string;
    petId: string;
    orderInfo: MatchingRecipes[];
}

export type GetVisitListFilterRequest =  {
    petHnId?: string;
    petName?: string;
    ownerName?: string;
    doctorId?: string;
    dateFrom?: string;
    dateTo?: string;
}
