import { QueryError, RepositoryError } from "../middlewares/errorHandler";
import { DatabaseErrorEnum as d } from "../enum/errors";
import { ResponseMessageEnum as m } from "../enum/response";
import { VisitAttributes } from "../entities/visits.entity";
import { withMongoDB } from "../db/mongodb";
import { GetVisitListFilterRequest } from "../modules/visits/dto/request";
import { Db } from "mongodb";
import { DATABASE_TABLE } from "../constants/database";
import { VisitWithOwnerDetailResponse, VisitWithOwnerListResponse } from "../modules/visits/dto/response";

type Filter = {
    isDeleted: boolean;
    "petInfo.hn"?: string;
    "petInfo.name"?: { $regex: string; $options: string };
    doctorId?: string;
    createdAt?: { $gte?: Date; $lte?: Date };
};

const DefaultFilter: Filter = {
    isDeleted: false,
};

export class VisitRepository {
    async initCollection(db: Db) {
        return db.collection<VisitAttributes>(DATABASE_TABLE.VISITS);
    }

    private getBaseVisitPipeline(query: any, ownerName?: string) {
        return [
            { $match: query },
            {
                $lookup: {
                    from: "pets",
                    localField: "petId",
                    foreignField: "id",
                    as: "petData",
                },
            },
            { $unwind: "$petData" },
            {
                $lookup: {
                    from: "owners",
                    localField: "petData.ownerId",
                    foreignField: "id",
                    as: "ownerData",
                },
            },
            { $unwind: "$ownerData" },
            ...(ownerName
                ? [
                      {
                          $match: {
                              $or: [
                                  { "ownerData.ownerInfo.firstName": { $regex: ownerName, $options: "i" } },
                                  { "ownerData.ownerInfo.lastName": { $regex: ownerName, $options: "i" } },
                                  {
                                      $expr: {
                                          $regexMatch: {
                                              input: {
                                                  $concat: [
                                                      "$ownerData.ownerInfo.firstName",
                                                      " ",
                                                      "$ownerData.ownerInfo.lastName",
                                                  ],
                                              },
                                              regex: ownerName,
                                              options: "i",
                                          },
                                      },
                                  },
                              ],
                          },
                      },
                  ]
                : []),
        ];
    }

    private visitListPipeline(query: any, ownerName?: string, offset: number, limit: number) {
        return [
            ...this.getBaseVisitPipeline(query, ownerName),
            {
                $project: {
                    _id: 0,
                    id: "$id",
                    petId: "$petId",
                    doctorId: "$doctorId",
                    petInfo: {
                        hn: "$petInfo.hn",
                        name: "$petInfo.name",
                        gender: "$petInfo.gender",
                        species: "$petInfo.species",
                        breed: "$petInfo.breed",
                    },
                    doctorInfo: {
                        prefix: "$doctorInfo.prefix",
                        firstName: "$doctorInfo.firstName",
                        lastName: "$doctorInfo.lastName",
                    },
                    ownerInfo: "$ownerData.ownerInfo",
                    visitStep: "$visitStep",
                    status: "$status",
                    createdAt: "$createdAt",
                },
            },
            { $sort: { createdAt: -1 } },
            { $skip: offset },
            { $limit: limit || 10 },
        ];
    }

    private visitListCountPipeline(query: any, ownerName?: string) {
        return [
            ...this.getBaseVisitPipeline(query, ownerName),
            { $count: "count" },
        ];
    }

    async findVisitById(id: string): Promise<VisitAttributes> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<VisitAttributes>("visits").findOne(
                    {
                        id: id,
                        isDeleted: false,
                    },
                    { projection: { _id: 0 } }
                );

                if (!response) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findAll(): Promise<VisitAttributes[]> {
        try {
            return await withMongoDB(async (db) => {
                const filter = { isDeleted: false };
                const response = await db
                    .collection<VisitAttributes>("visits")
                    .find(filter, {
                        projection: {
                            _id: 0,
                        },
                    })
                    .toArray();
                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findList(
        limit: number,
        page: number,
        filter?: GetVisitListFilterRequest
    ): Promise<VisitWithOwnerListResponse> {
        try {
            const query: Filter = { isDeleted: false };
            let ownerName: string | undefined;

            if (filter) {
                const { petHnId, petName, ownerName: filterOwnerName, doctorId, dateFrom, dateTo } = filter;
                ownerName = filterOwnerName;
                if (filter.petHnId) query["petInfo.hn"] = petHnId;
                if (petName) query["petInfo.name"] = { $regex: `^${petName}`, $options: "i" }; // petName%
                if (doctorId) query.doctorId = doctorId;
                if (dateFrom || dateTo) {
                    query.createdAt = {};
                    if (dateFrom) query.createdAt.$gte = new Date(dateFrom);
                    if (dateTo) query.createdAt.$lte = new Date(dateTo);
                }
            }

            const offset = (page - 1) * limit;

            return await withMongoDB(async (db: Db) => {
                const collection = await this.initCollection(db);

                const rowsPipeline = this.visitListPipeline(query, ownerName, offset, limit);
                const rows = await collection.aggregate<VisitWithOwnerDetailResponse>(rowsPipeline).toArray();
                
                const countPipeline = this.visitListCountPipeline(query, ownerName);
                const countResult = await collection.aggregate(countPipeline).toArray();
                const count = countResult.length > 0 ? countResult[0].count : 0;


                const response: VisitWithOwnerListResponse = {
                    rows,
                    count,
                };
                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async create(data: VisitAttributes): Promise<VisitAttributes> {
        try {
            return await withMongoDB(async (db) => {
                const visitData = {
                    ...data,
                    isDeleted: false,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    deletedAt: null,
                };
                const createdVisit = await db.collection<VisitAttributes>("visits").insertOne(visitData);

                if (!createdVisit) {
                    throw new QueryError(m.INSERTION_FAILED);
                }

                const response = await this.findVisitById(visitData.id);

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async update(id: string, data: Partial<VisitAttributes>): Promise<boolean> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<VisitAttributes>("visits").updateOne(
                    { id: id, isDeleted: false },
                    {
                        $set: {
                            ...data,
                            updatedAt: new Date(),
                        },
                    }
                );
                if (response.matchedCount === 0) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);
                return response.modifiedCount === 1;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }
}

export default new VisitRepository();
