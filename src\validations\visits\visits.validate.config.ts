import { FieldConfigMap } from "../../types/validate.type";
import {
    VisitValidateType,
    PetHealthSubmitValidateType,
    PetRequirementSubmitValidateType,
    PetNutritionSubmitValidateType,
    PetProductMatchSubmitValidateType,
    GetVisitListFilterRequest
} from "./visits.type";
import { ResponseValidationTypeEnum as v } from "../../enum/validate";
import { VisitStepEnum, VisitStatusEnum } from "../../enum/visit";

export const createConfig: FieldConfigMap<VisitValidateType> = {
    petId: { type: v.UUID, required: true },
    doctorId: { type: v.UUID, required: true },
    petInfo: { type: v.PET_INFO, required: true },
    doctorInfo: { type: v.DOCTOR_INFO, required: true },
    visitStep: {
        type: v.ENUM,
        required: true,
        enumValues: Object.values(VisitStepEnum),
    },
    status: {
        type: v.ENUM,
        required: true,
        enumValues: Object.values(VisitStatusEnum),
    },
    petHealth: { type: v.PET_HEALTH, required: true, allowNull: true },
    petRequirement: { type: v.PET_REQUIREMENT, required: true, allowNull: true },
    petNutrition: { type: v.PET_NUTRITION, required: true, allowNull: true },
    petProductMatch: { type: v.PET_PRODUCT_MATCH, required: true, allowNull: true },
};

export const petHealthValidateConfig: FieldConfigMap<PetHealthSubmitValidateType> = {
    petHealth: { type: v.PET_HEALTH, required: false, allowNull: true },
};

export const petRequirementValidateConfig: FieldConfigMap<PetRequirementSubmitValidateType> = {
    petRequirement: { type: v.PET_REQUIREMENT, required: false, allowNull: true },
};

export const petNutritionValidateConfig: FieldConfigMap<PetNutritionSubmitValidateType> = {
    petNutrition: { type: v.PET_NUTRITION, required: false, allowNull: true },
};

export const petProductMatchValidateConfig: FieldConfigMap<PetProductMatchSubmitValidateType> = {
    petProductMatch: { type: v.PET_PRODUCT_MATCH, required: false, allowNull: true },
};

export const filterConfig: FieldConfigMap<GetVisitListFilterRequest> = {
    petHnId: { type: v.STRING, required: false },
    petName: { type: v.STRING, required: false },
    ownerName: { type: v.STRING, required: false },
    doctorId: { type: v.UUID, required: false },
    dateFrom: { type: v.DATE, required: false },
    dateTo: { type: v.DATE, required: false },
};
