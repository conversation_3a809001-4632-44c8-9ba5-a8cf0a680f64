import { v7 as uuidv7 } from "uuid";
import {
    PetGenderTypeEnum,
    PetSpeciesEnum,
    PetLivingEnvironmentEnum,
    ActiveScoreVisualLevelEnum,
    ActiveScoreCalculationMethodEnum,
} from "../../../enum/pet";
import { VisitStepEnum, VisitStatusEnum } from "../../../enum/visit";
import { ResponseMessageEnum as m, ResponseStatusCodeEnum as s } from "../../../enum/response";
import { ErrorTypeEnum as e } from "../../../enum/errors";
import { DefaultError } from "../../../middlewares/errorHandler";
import { ResponseValidationMessageEnum as v } from "../../../enum/validate";

const validDoctorUUID = uuidv7();
const validOwnerUUID = uuidv7();
const validDiseaseUUID = uuidv7();
const validPetUUID = uuidv7();

const invalidVisitStep = "One";
const invalidVisitStatus = "Waiting";

// Mock petInfo Data
const petMockData = {
    validData: {
        validPetInfoRequest: {
            hn: "HN0001",
            name: "Test Pet",
            gender: PetGenderTypeEnum.MALE,
            species: PetSpeciesEnum.DOG,
            breed: "pitbull",
            isMixedBreed: false,
            birthDate: "2025-01-01T12:00:00Z",
            weights: [
                {
                    weight: 3.2,
                    measuredDate: "2025-01-01T12:00:00Z",
                },
            ],
            livingEnvironment: PetLivingEnvironmentEnum.INDOOR,
            isNeutering: true,
            diseases: [
                {
                    diseaseId: validDiseaseUUID,
                    diseaseName: "HIV",
                    validFrom: "2025-01-01T12:00:00Z",
                    validTo: "2025-01-01T12:00:00Z",
                    createdAt: "2025-01-01T12:00:00Z",
                    updatedAt: "2025-01-01T12:00:00Z",
                },
            ],
            activeScore: {
                score: 5,
                calculationMethod: ActiveScoreCalculationMethodEnum.VISUAL,
                questionaires: null,
                visualLevel: ActiveScoreVisualLevelEnum.VERY_ACTIVE,
            },
        },
    },
    invalidData: {
        empty: {},
        invalidPetInfoRequest: {
            hn: 1234,
            name: "",
            gender: "Female",
            species: "bird",
            breed: "",
            isMixedBreed: "true",
            birthDate: "2019-07-10",
            weights: [
                {
                    weight: "3.2",
                    measuredDate: "2025-07-38T00:00:00.000Z",
                },
            ],
            livingEnvironment: "Indoor",
            isNeutering: "true",
            diseases: [
                {
                    diseaseId: 12345,
                    diseaseName: null,
                    validFrom: "2019-07-10",
                    validTo: "2019-07-10",
                    createdAt: "2019-07-10",
                    updatedAt: "2019-07-10",
                },
            ],
            activeScore: {
                score: "5",
                calculationMethod: "Visual",
                questionaires: "null",
                visualLevel: "veryactive",
            },
        },
    },
};

// Mock ownerInfo Data
const ownerMockData = {
    validData: {
        validOwnerInfoRequest: {
            firstName: "John",
            lastName: "Doe",
            phoneNumber: "081234567890",
            email: "<EMAIL>",
        },
    },
    invalidData: {
        empty: {},
        invalidOwnerInfoRequest: {
            firstName: "",
            lastName: "",
            phoneNumber: 12345678,
            email: "john.doe@example",
        },
    },
};

// Mock doctorInfo Data
const doctorMockData = {
    validData: {
        validDoctorInfoRequest: {
            prefix: "Dr.",
            firstName: "Steve",
            lastName: "Rogers",
            phoneNumber: "0992861756",
            email: "<EMAIL>",
        },
    },
    invalidData: {
        empty: {},
        invalidDoctorInfoRequest: {
            prefix: "",
            firstName: "",
            lastName: "",
            phoneNumber: 12345678,
            email: "steve@gmail",
        },
    },
};

// Mock createWithOwner from PetModule Data
const createWithOwnerMockData = {
    success: {
        pet: {
            id: validPetUUID,
            ownerId: validOwnerUUID,
            petInfo: {
                hn: "HN0001",
                name: "Test Pet",
                gender: PetGenderTypeEnum.MALE,
                species: PetSpeciesEnum.DOG,
                breed: "pitbull",
                isMixedBreed: false,
                birthDate: new Date("2025-01-01T12:00:00Z"),
                weights: [
                    {
                        weight: 3.2,
                        measuredDate: new Date("2025-01-01T12:00:00Z"),
                    },
                ],
                livingEnvironment: PetLivingEnvironmentEnum.INDOOR,
                isNeutering: true,
                diseases: [
                    {
                        diseaseId: validDiseaseUUID,
                        diseaseName: "HIV",
                        validFrom: new Date("2025-01-01T12:00:00Z"),
                        validTo: new Date("2025-01-01T12:00:00Z"),
                        createdAt: new Date("2025-01-01T12:00:00Z"),
                        updatedAt: new Date("2025-01-01T12:00:00Z"),
                    },
                ],
                activeScore: {
                    score: 5,
                    calculationMethod: ActiveScoreCalculationMethodEnum.VISUAL,
                    questionaires: null,
                    visualLevel: ActiveScoreVisualLevelEnum.VERY_ACTIVE,
                },
            },
            isDeleted: false,
            deletedAt: null,
            createdAt: new Date("2025-01-01T12:00:00Z"),
            updatedAt: new Date("2025-01-01T12:00:00Z"),
        },
        owner: {
            id: validOwnerUUID,
            ownerInfo: ownerMockData.validData.validOwnerInfoRequest,
            isDeleted: false,
            deletedAt: null,
            createdAt: new Date("2025-01-01T12:00:00Z"),
            updatedAt: new Date("2025-01-01T12:00:00Z"),
        },
    },
    error: {
        invalidOwnerInfo: new DefaultError(e.VALIDATION_ERROR, m.INVALID_FORMAT, null, {
            ownerInfo: {
                firstName: {
                    _error: v.MIN_CHAR,
                    _min: 1,
                },
                lastName: {
                    _error: v.MIN_CHAR,
                    _min: 1,
                },
                phoneNumber: v.PHONE,
                email: v.EMAIL,
            },
        }),
        invalidPetInfo: new DefaultError(e.VALIDATION_ERROR, m.INVALID_FORMAT, null, {
            petInfo: {
                hn: {
                    _error: v.MIN_CHAR,
                    _min: 1,
                },
                name: {
                    _error: v.MIN_CHAR,
                    _min: 1,
                },
                gender: v.ENUM,
                species: v.ENUM,
                breed: {
                    _error: v.MIN_CHAR,
                    _min: 1,
                },
                isMixedBreed: v.BOOLEAN,
                birthDate: v.DATE,
                weights: [
                    {
                        _index: 0,
                        weight: v.NUMBER,
                        measuredDate: v.DATE,
                    },
                ],
                livingEnvironment: v.ENUM,
                isNeutering: v.BOOLEAN,
                diseases: [
                    {
                        _index: 0,
                        diseaseId: v.STRING,
                        diseaseName: v.STRING,
                        validFrom: v.DATE,
                        validTo: v.DATE,
                        createdAt: v.DATE,
                        updatedAt: v.DATE,
                    },
                ],
                activeScore: {
                    score: v.NUMBER,
                    calculationMethod: v.ENUM,
                    questionaires: v.ARRAY,
                    visualLevel: v.ENUM,
                },
            },
        }),
        petHNAlreadyExist: new DefaultError(
            e.DATABASE_ERROR,
            m.DUPLICATED_DATA,
            null,
            {
                petInfo: {
                    hn: m.DUPLICATED_HNID,
                },
            },
            s.CONFLICT
        ),
        ownerPhoneNumberAlreadyExist: new DefaultError(
            e.DATABASE_ERROR,
            m.DUPLICATED_DATA,
            null,
            {
                ownerInfo: {
                    phoneNumber: m.DUPLICATED_PHONE_NUMBER,
                },
            },
            s.CONFLICT
        ),
        notDefineOwnerIdandOwnerInfo: new DefaultError(
            e.VALIDATION_ERROR,
            m.OWNER_INFO_OR_ID_REQUIRED,
            null,
            {
                ownerInfo: m.OWNER_INFO_OR_ID_REQUIRED,
            },
            s.BAD_REQUEST
        ),
        defineBothOwnerIdandOwnerInfo: new DefaultError(
            e.VALIDATION_ERROR,
            m.OWNER_INFO_AND_ID_CONFLICT,
            null,
            {
                ownerInfo: m.OWNER_INFO_AND_ID_CONFLICT,
            },
            s.BAD_REQUEST
        ),
        emptyOwnerInfo: new DefaultError(
            e.VALIDATION_ERROR,
            m.INVALID_FORMAT,
            null,
            {
                ownerInfo: {
                    firstName: v.REQUIRED,
                    lastName: v.REQUIRED,
                    phoneNumber: v.REQUIRED,
                    email: v.REQUIRED,
                },
            },
            s.BAD_REQUEST
        ),
        emptyPetInfo: new DefaultError(
            e.VALIDATION_ERROR,
            m.INVALID_FORMAT,
            null,
            {
                petInfo: {
                    hn: v.REQUIRED,
                    name: v.REQUIRED,
                    gender: v.REQUIRED,
                    species: v.REQUIRED,
                    breed: v.REQUIRED,
                    isMixedBreed: v.REQUIRED,
                    birthDate: v.REQUIRED,
                    weights: v.REQUIRED,
                    livingEnvironment: v.REQUIRED,
                    isNeutering: v.REQUIRED,
                    diseases: v.REQUIRED,
                    activeScore: v.REQUIRED,
                },
            },
            s.BAD_REQUEST
        ),
        unexpectedErrorFromPetModule: new DefaultError(e.INTERNAL_SERVER_ERROR, m.CREATE_PET_WITH_OWNER_FAILED),
    },
};

// Mock createVisitFromPet from VisitModule Data
const createVisitFromPetMockData = {
    success: undefined,
    error: {
        invalidDoctorInfo: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                doctorInfo: {
                    prefix: {
                        _error: v.MIN_CHAR,
                        _min: 1,
                    },
                    firstName: {
                        _error: v.MIN_CHAR,
                        _min: 1,
                    },
                    lastName: {
                        _error: v.MIN_CHAR,
                        _min: 1,
                    },
                    phoneNumber: v.STRING,
                    email: v.EMAIL,
                },
            },
        },
        invalidVisitAttributes: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                doctorId: v.STRING,
                visitStep: v.ENUM,
                status: v.ENUM,
                petHealth: v.REQUIRED,
                petRequirement: v.REQUIRED,
                petNutrition: v.REQUIRED,
                petProductMatch: v.REQUIRED,
            },
        },
        unexpectedErrorFromPetModule: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.CREATE_PET_WITH_OWNER_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        unexpectedErrorFromVisitModule: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.CREATE_VISIT_FROM_PET_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
    },
};

// Case 1 : [Success] create new visit with new pet and new owner
const validWithNewOwner = {
    ownerId: null,
    doctorId: validDoctorUUID,
    visitStep: VisitStepEnum.STEP_ONE,
    status: VisitStatusEnum.WAITING,
    petInfo: petMockData.validData.validPetInfoRequest,
    ownerInfo: ownerMockData.validData.validOwnerInfoRequest,
    doctorInfo: doctorMockData.validData.validDoctorInfoRequest,
    petHealth: null,
    petRequirement: null,
    petNutrition: null,
    petProductMatch: null,
};

// Case 2 : [Success] create new visit with new pet and existing owner
const validWithExistOwner = {
    ownerId: validOwnerUUID,
    doctorId: validDoctorUUID,
    visitStep: VisitStepEnum.STEP_ONE,
    status: VisitStatusEnum.WAITING,
    petInfo: petMockData.validData.validPetInfoRequest,
    ownerInfo: null,
    doctorInfo: doctorMockData.validData.validDoctorInfoRequest,
    petHealth: null,
    petRequirement: null,
    petNutrition: null,
    petProductMatch: null,
};

// Case 3 : [Failed] create new visit with invalid owner info
const invalidOwnerInfo = {
    ownerId: null,
    doctorId: validDoctorUUID,
    visitStep: VisitStepEnum.STEP_ONE,
    status: VisitStatusEnum.WAITING,
    petInfo: petMockData.validData.validPetInfoRequest,
    ownerInfo: ownerMockData.invalidData.invalidOwnerInfoRequest,
    doctorInfo: doctorMockData.validData.validDoctorInfoRequest,
    petHealth: null,
    petRequirement: null,
    petNutrition: null,
    petProductMatch: null,
};

// Case 4 : [Failed] create new visit with invalid pet info
const invalidPetInfo = {
    ownerId: null,
    doctorId: validDoctorUUID,
    visitStep: VisitStepEnum.STEP_ONE,
    status: VisitStatusEnum.WAITING,
    petInfo: petMockData.invalidData.invalidPetInfoRequest,
    ownerInfo: ownerMockData.validData.validOwnerInfoRequest,
    doctorInfo: doctorMockData.validData.validDoctorInfoRequest,
    petHealth: null,
    petRequirement: null,
    petNutrition: null,
    petProductMatch: null,
};

// Case 5 : [Failed] create new visit with invalid doctor info
const invalidDoctorInfo = {
    ownerId: null,
    doctorId: validDoctorUUID,
    visitStep: VisitStepEnum.STEP_ONE,
    status: VisitStatusEnum.WAITING,
    petInfo: petMockData.validData.validPetInfoRequest,
    ownerInfo: ownerMockData.validData.validOwnerInfoRequest,
    doctorInfo: doctorMockData.invalidData.invalidDoctorInfoRequest,
    petHealth: null,
    petRequirement: null,
    petNutrition: null,
    petProductMatch: null,
};

// Case 6 : [Failed] create new visit with invalid visit attributes
const invalidVisitAttributes = {
    ownerId: null,
    doctorId: null,
    visitStep: invalidVisitStep,
    status: invalidVisitStatus,
    petInfo: petMockData.validData.validPetInfoRequest,
    ownerInfo: ownerMockData.validData.validOwnerInfoRequest,
    doctorInfo: doctorMockData.validData.validDoctorInfoRequest,
};

// Case 7 : [Failed] create new visit with existing pet hn
const existingPetHN = {
    ownerId: null,
    doctorId: validDoctorUUID,
    visitStep: VisitStepEnum.STEP_ONE,
    status: VisitStatusEnum.WAITING,
    petInfo: petMockData.validData.validPetInfoRequest,
    ownerInfo: ownerMockData.validData.validOwnerInfoRequest,
    doctorInfo: doctorMockData.validData.validDoctorInfoRequest,
    petHealth: null,
    petRequirement: null,
    petNutrition: null,
    petProductMatch: null,
};

// Case 8 : [Failed] create new visit with existing owner phone number
const existingOwnerPhoneNumber = {
    ownerId: null,
    doctorId: validDoctorUUID,
    visitStep: VisitStepEnum.STEP_ONE,
    status: VisitStatusEnum.WAITING,
    petInfo: petMockData.validData.validPetInfoRequest,
    ownerInfo: ownerMockData.validData.validOwnerInfoRequest,
    doctorInfo: doctorMockData.validData.validDoctorInfoRequest,
    petHealth: null,
    petRequirement: null,
    petNutrition: null,
    petProductMatch: null,
};

// Case 9 : [Failed] create new visit with not define owner id and owner info
const notDefineOwnerIdandOwnerInfo = {
    ownerId: null,
    doctorId: validDoctorUUID,
    visitStep: VisitStepEnum.STEP_ONE,
    status: VisitStatusEnum.WAITING,
    petInfo: petMockData.validData.validPetInfoRequest,
    ownerInfo: null,
    doctorInfo: doctorMockData.validData.validDoctorInfoRequest,
    petHealth: null,
    petRequirement: null,
    petNutrition: null,
    petProductMatch: null,
};

// Case 10 : [Failed] create new visit with define both owner id and owner info
const defineBothOwnerIdandOwnerInfo = {
    ownerId: validOwnerUUID,
    doctorId: validDoctorUUID,
    visitStep: VisitStepEnum.STEP_ONE,
    status: VisitStatusEnum.WAITING,
    petInfo: petMockData.validData.validPetInfoRequest,
    ownerInfo: ownerMockData.validData.validOwnerInfoRequest,
    doctorInfo: doctorMockData.validData.validDoctorInfoRequest,
    petHealth: null,
    petRequirement: null,
    petNutrition: null,
    petProductMatch: null,
};

// Case 11 : [Failed] create new visit with empty ownerInfo request body
const emptyOwnerInfo = {
    ownerId: null,
    doctorId: validDoctorUUID,
    visitStep: VisitStepEnum.STEP_ONE,
    status: VisitStatusEnum.WAITING,
    petInfo: petMockData.validData.validPetInfoRequest,
    ownerInfo: ownerMockData.invalidData.empty,
    doctorInfo: doctorMockData.validData.validDoctorInfoRequest,
    petHealth: null,
    petRequirement: null,
    petNutrition: null,
    petProductMatch: null,
};

// Case 12 : [Failed] create new visit with empty petInfo request body
const emptyPetInfo = {
    ownerId: null,
    doctorId: validDoctorUUID,
    visitStep: VisitStepEnum.STEP_ONE,
    status: VisitStatusEnum.WAITING,
    petInfo: petMockData.invalidData.empty,
    ownerInfo: ownerMockData.validData.validOwnerInfoRequest,
    doctorInfo: doctorMockData.validData.validDoctorInfoRequest,
    petHealth: null,
    petRequirement: null,
    petNutrition: null,
    petProductMatch: null,
};

export const createFromPetMocks = {
    request: {
        validBody: {
            validWithNewOwner: validWithNewOwner,
            validWithExistOwner: validWithExistOwner,
        },
        invalidBody: {
            invalidOwnerInfo: invalidOwnerInfo,
            invalidPetInfo: invalidPetInfo,
            invalidDoctorInfo: invalidDoctorInfo,
            invalidVisitAttributes: invalidVisitAttributes,
            existingPetHN: existingPetHN,
            existingOwnerPhoneNumber: existingOwnerPhoneNumber,
            notDefineOwnerIdandOwnerInfo: notDefineOwnerIdandOwnerInfo,
            defineBothOwnerIdandOwnerInfo: defineBothOwnerIdandOwnerInfo,
            emptyOwnerInfo: emptyOwnerInfo,
            emptyPetInfo: emptyPetInfo,
        },
    },
    response: {
        success: createVisitFromPetMockData.success,
    },
    petModuleResponse: {
        success: createWithOwnerMockData.success,
        error: {
            invalidOwnerInfo: createWithOwnerMockData.error.invalidOwnerInfo,
            invalidPetInfo: createWithOwnerMockData.error.invalidPetInfo,
            petHNAlreadyExist: createWithOwnerMockData.error.petHNAlreadyExist,
            ownerPhoneNumberAlreadyExist: createWithOwnerMockData.error.ownerPhoneNumberAlreadyExist,
            notDefineOwnerIdandOwnerInfo: createWithOwnerMockData.error.notDefineOwnerIdandOwnerInfo,
            defineBothOwnerIdandOwnerInfo: createWithOwnerMockData.error.defineBothOwnerIdandOwnerInfo,
            emptyOwnerInfo: createWithOwnerMockData.error.emptyOwnerInfo,
            emptyPetInfo: createWithOwnerMockData.error.emptyPetInfo,
            unexpectedError: createWithOwnerMockData.error.unexpectedErrorFromPetModule,
        },
    },
    error: {
        invalidOwnerInfo: createWithOwnerMockData.error.invalidOwnerInfo,
        invalidPetInfo: createWithOwnerMockData.error.invalidPetInfo,
        invalidDoctorInfo: createVisitFromPetMockData.error.invalidDoctorInfo,
        invalidVisitAttributes: createVisitFromPetMockData.error.invalidVisitAttributes,
        petHNAlreadyExist: createWithOwnerMockData.error.petHNAlreadyExist,
        ownerPhoneNumberAlreadyExist: createWithOwnerMockData.error.ownerPhoneNumberAlreadyExist,
        notDefineOwnerIdandOwnerInfo: createWithOwnerMockData.error.notDefineOwnerIdandOwnerInfo,
        defineBothOwnerIdandOwnerInfo: createWithOwnerMockData.error.defineBothOwnerIdandOwnerInfo,
        emptyOwnerInfo: createWithOwnerMockData.error.emptyOwnerInfo,
        emptyPetInfo: createWithOwnerMockData.error.emptyPetInfo,
        unexpectedErrorFromPetModule: createVisitFromPetMockData.error.unexpectedErrorFromPetModule,
        unexpectedErrorFromVisitModule: createVisitFromPetMockData.error.unexpectedErrorFromVisitModule,
    },
};

const validVisitUUID1 = uuidv7();
const validVisitUUID2 = uuidv7();
const validPetUUID1 = uuidv7();
const validPetUUID2 = uuidv7();
const validDoctorUUID1 = uuidv7();
const validDoctorUUID2 = uuidv7();

const invalidVisitUUID = "invalidUUID";

const getListMocksData = {
    success: {
        defaultFilter: {
            rows: [
                {
                    petInfo: {
                        hn: "HN1",
                        name: "Leno",
                        gender: PetGenderTypeEnum.MALE,
                        species: PetSpeciesEnum.DOG,
                        breed: "Labrador",
                    },
                    doctorInfo: {
                        prefix: "Dr.",
                        firstName: "John",
                        lastName: "Doe",
                    },
                    ownerInfo: {
                        firstName: "John",
                        lastName: "Doe",
                        phoneNumber: "0912345678",
                        email: "<EMAIL>",
                    },
                    id: validVisitUUID1,
                    petId: validVisitUUID1,
                    doctorId: validVisitUUID1,
                    visitStep: VisitStepEnum.STEP_ONE,
                    status: VisitStatusEnum.WAITING,
                    createdAt: new Date("2025-08-12T12:00:00"),
                },
                {
                    petInfo: {
                        hn: "HN2",
                        name: "Milo",
                        gender: PetGenderTypeEnum.MALE,
                        species: PetSpeciesEnum.DOG,
                        breed: "Golden Retriever",
                    },
                    doctorInfo: {
                        prefix: "Dr.",
                        firstName: "John",
                        lastName: "Doe",
                        phoneNumber: "0912345678",
                        email: "<EMAIL>",
                    },
                    ownerInfo: {
                        firstName: "John",
                        lastName: "Doe",
                        phoneNumber: "0912345678",
                        email: "<EMAIL>",
                    },
                    id: validVisitUUID2,
                    petId: validPetUUID2,
                    doctorId: validDoctorUUID2,
                    visitStep: VisitStepEnum.STEP_TWO,
                    status: VisitStatusEnum.INPROGRESS,
                    createdAt: new Date("2025-08-12T12:00:00"),
                },
            ],
            count: 4,
        },
    },
};

const getListDefaultFilter = {
    limit: 2,
    page: 1,
};

export const getListMocks = {
    request: {
        validParams: {
            // Case 1 : [Success] Get list with default filter
            defaultFilter: {
                ...getListDefaultFilter,
            },
            // Case 2 : [Success] Get list with pet hn id filter
            petHnIdFilter: {
                ...getListDefaultFilter,
                petHnId: "HN1",
            },
            // Case 3 : [Success] Get list with pet name filter
            petNameFilter: {
                ...getListDefaultFilter,
                petName: "Leno",
            },
            // Case 4 : [Success] Get list with owner firstname filter
            ownerFirstNameFilter: {
                ...getListDefaultFilter,
                ownerName: "John",
            },
            // Case 5 : [Success] Get list with owner lastname filter
            ownerLastNameFilter: {
                ...getListDefaultFilter,
                ownerName: "Doe",
            },
            // Case 6 : [Success] Get list with owner fullname filter
            ownerFullNameFilter: {
                ...getListDefaultFilter,
                ownerName: "John Doe",
            },
            // Case 7 : [Success] Get list with doctor id filter
            doctorIdFilter: {
                ...getListDefaultFilter,
                doctorId: validDoctorUUID1,
            },
            // Case 8 : [Success] Get list with date from filter
            dateFromFilter: {
                ...getListDefaultFilter,
                dateFrom: "2025-08-12T00:00:00",
            },
            // Case 9 : [Success] Get list with date to filter
            dateToFilter: {
                ...getListDefaultFilter,
                dateTo: "2025-09-12T00:00:00",
            },
            // Case 10 : [Success] Get list with date range filter
            dateRangeFilter: {
                ...getListDefaultFilter,
                dateFrom: "2025-08-12T00:00:00",
                dateTo: "2025-09-12T00:00:00",
            },
        },
        invalidParams: {
            // Case 11 : [Failed] Get list with invalid limit
            invalidLimit: {
                limit: "2",
                page: 1,
            },
            // Case 12 : [Failed] Get list with invalid page
            invalidPage: {
                limit: 2,
                page: "1",
            },
            // Case 13 : [Failed] Get list with invalid pet hn id
            invalidPetHnId: {
                ...getListDefaultFilter,
                petHnId: 1,
            },
            // Case 14 : [Failed] Get list with invalid pet name
            invalidPetName: {
                ...getListDefaultFilter,
                petName: 1,
            },
            // Case 15 : [Failed] Get list with invalid owner name
            invalidOwnerName: {
                ...getListDefaultFilter,
                ownerName: 1,
            },
            // Case 16 : [Failed] Get list with invalid doctor id
            invalidDoctorId: {
                ...getListDefaultFilter,
                doctorId: 1,
            },
            // Case 17 : [Failed] Get list with invalid date from
            invalidDateFrom: {
                ...getListDefaultFilter,
                dateFrom: "invalidDate",
            },
            // Case 18 : [Failed] Get list with invalid date to
            invalidDateTo: {
                ...getListDefaultFilter,
                dateTo: "invalidDate",
            },
            // Case 19 : [Failed] Get list with invalid date range
            invalidDateRange: {
                ...getListDefaultFilter,
                dateFrom: "2021-01-01T00:00:00",
                dateTo: "2020-01-01T00:00:00",
            },
        },
    },
    response: {
        defaultFilter: {
            rows: [
                {
                    petInfo: {
                        hn: "HN1",
                        name: "Leno",
                        gender: PetGenderTypeEnum.MALE,
                        species: PetSpeciesEnum.DOG,
                        breed: "Labrador",
                    },
                    doctorInfo: {
                        prefix: "Dr.",
                        firstName: "Steve",
                        lastName: "Rogers",
                    },
                    ownerInfo: {
                        firstName: "John",
                        lastName: "Doe",
                        phoneNumber: "0912345678",
                        email: "<EMAIL>",
                    },
                    id: validVisitUUID1,
                    petId: validVisitUUID1,
                    doctorId: validVisitUUID1,
                    visitStep: VisitStepEnum.STEP_ONE,
                    status: VisitStatusEnum.WAITING,
                    createdAt: new Date("2025-08-12T12:00:00"),
                },
                {
                    petInfo: {
                        hn: "HN2",
                        name: "Milo",
                        gender: PetGenderTypeEnum.MALE,
                        species: PetSpeciesEnum.DOG,
                        breed: "Golden Retriever",
                    },
                    doctorInfo: {
                        prefix: "Dr.",
                        firstName: "John",
                        lastName: "Doe",
                        phoneNumber: "0912345678",
                        email: "<EMAIL>",
                    },
                    ownerInfo: {
                        firstName: "John",
                        lastName: "Doe",
                        phoneNumber: "0912345678",
                        email: "<EMAIL>",
                    },
                    id: validVisitUUID2,
                    petId: validPetUUID2,
                    doctorId: validDoctorUUID2,
                    visitStep: VisitStepEnum.STEP_TWO,
                    status: VisitStatusEnum.INPROGRESS,
                    createdAt: new Date("2025-08-12T13:00:00"),
                },
            ],
            count: 4,
        },
        petHnIdFilter: {
            rows: [
                {
                    petInfo: {
                        hn: "HN1",
                        name: "Leno",
                        gender: PetGenderTypeEnum.MALE,
                        species: PetSpeciesEnum.DOG,
                        breed: "Labrador",
                    },
                    doctorInfo: {
                        prefix: "Dr.",
                        firstName: "Steve",
                        lastName: "Rogers",
                    },
                    ownerInfo: {
                        firstName: "John",
                        lastName: "Doe",
                        phoneNumber: "0912345678",
                        email: "<EMAIL>",
                    },
                    id: validVisitUUID1,
                    petId: validVisitUUID1,
                    doctorId: validVisitUUID1,
                    visitStep: VisitStepEnum.STEP_ONE,
                    status: VisitStatusEnum.WAITING,
                    createdAt: new Date("2025-08-12T12:00:00"),
                },
            ],
            count: 1,
        },
        petNameFilter: { 
            rows: [
                {
                    petInfo: {
                        hn: "HN1",
                        name: "Leno",
                        gender: PetGenderTypeEnum.MALE,
                        species: PetSpeciesEnum.DOG,
                        breed: "Labrador",
                    },
                    doctorInfo: {
                        prefix: "Dr.",
                        firstName: "Steve",
                        lastName: "Rogers",
                    },
                    ownerInfo: {
                        firstName: "John",
                        lastName: "Doe",
                        phoneNumber: "0912345678",
                        email: "<EMAIL>",
                    },
                    id: validVisitUUID1,
                    petId: validVisitUUID1,
                    doctorId: validVisitUUID1,
                    visitStep: VisitStepEnum.STEP_ONE,
                    status: VisitStatusEnum.WAITING,
                    createdAt: new Date("2025-08-12T12:00:00"),
                }
            ], 
            count: 1
        },
        ownerFirstNameFilter: { 
            rows: [
                {
                    petInfo: {
                        hn: "HN1",
                        name: "Leno",
                        gender: PetGenderTypeEnum.MALE,
                        species: PetSpeciesEnum.DOG,
                        breed: "Labrador",
                    },
                    doctorInfo: {
                        prefix: "Dr.",
                        firstName: "Steve",
                        lastName: "Rogers",
                    },
                    ownerInfo: {
                        firstName: "John",
                        lastName: "Doe",
                        phoneNumber: "0912345678",
                        email: "<EMAIL>",
                    },
                    id: validVisitUUID1,
                    petId: validVisitUUID1,
                    doctorId: validVisitUUID1,
                    visitStep: VisitStepEnum.STEP_ONE,
                    status: VisitStatusEnum.WAITING,
                    createdAt: new Date("2025-08-12T12:00:00"),
                }
            ], 
            count: 1 
        },
        ownerLastNameFilter: { 
            rows: [
                {
                    petInfo: {
                        hn: "HN1",
                        name: "Leno",
                        gender: PetGenderTypeEnum.MALE,
                        species: PetSpeciesEnum.DOG,
                        breed: "Labrador",
                    },
                    doctorInfo: {
                        prefix: "Dr.",
                        firstName: "Steve",
                        lastName: "Rogers",
                    },
                    ownerInfo: {
                        firstName: "John",
                        lastName: "Doe",
                        phoneNumber: "0912345678",
                        email: "<EMAIL>",
                    },
                    id: validVisitUUID1,
                    petId: validVisitUUID1,
                    doctorId: validVisitUUID1,
                    visitStep: VisitStepEnum.STEP_ONE,
                    status: VisitStatusEnum.WAITING,
                    createdAt: new Date("2025-08-12T12:00:00"),
                }
            ], 
            count: 1
        },
        ownerFullNameFilter: { 
            rows: [
                {
                    petInfo: {
                        hn: "HN1",
                        name: "Leno",
                        gender: PetGenderTypeEnum.MALE,
                        species: PetSpeciesEnum.DOG,
                        breed: "Labrador",
                    },
                    doctorInfo: {
                        prefix: "Dr.",
                        firstName: "Steve",
                        lastName: "Rogers",
                    },
                    ownerInfo: {
                        firstName: "John",
                        lastName: "Doe",
                        phoneNumber: "0912345678",
                        email: "<EMAIL>",
                    },
                    id: validVisitUUID1,
                    petId: validVisitUUID1,
                    doctorId: validVisitUUID1,
                    visitStep: VisitStepEnum.STEP_ONE,
                    status: VisitStatusEnum.WAITING,
                    createdAt: new Date("2025-08-12T12:00:00"),
                }
            ], 
            count: 0 
        },
        doctorIdFilter: { 
            rows: [
                {
                    petInfo: {
                        hn: "HN1",
                        name: "Leno",
                        gender: PetGenderTypeEnum.MALE,
                        species: PetSpeciesEnum.DOG,
                        breed: "Labrador",
                    },
                    doctorInfo: {
                        prefix: "Dr.",
                        firstName: "Steve",
                        lastName: "Rogers",
                    },
                    ownerInfo: {
                        firstName: "John",
                        lastName: "Doe",
                        phoneNumber: "0912345678",
                        email: "<EMAIL>",
                    },
                    id: validVisitUUID1,
                    petId: validVisitUUID1,
                    doctorId: validVisitUUID1,
                    visitStep: VisitStepEnum.STEP_ONE,
                    status: VisitStatusEnum.WAITING,
                    createdAt: new Date("2025-08-12T12:00:00"),
                },
                {
                    petInfo: {
                        hn: "HN2",
                        name: "Milo",
                        gender: PetGenderTypeEnum.MALE,
                        species: PetSpeciesEnum.DOG,
                        breed: "Golden Retriever",
                    },
                    doctorInfo: {
                        prefix: "Dr.",
                        firstName: "Steve",
                        lastName: "Rogers",
                    },
                    ownerInfo: {
                        firstName: "John",
                        lastName: "Doe",
                        phoneNumber: "0912345678",
                        email: "<EMAIL>",
                    },
                    id: validVisitUUID2,
                    petId: validVisitUUID2,
                    doctorId: validVisitUUID1,
                    visitStep: VisitStepEnum.STEP_TWO,
                    status: VisitStatusEnum.INPROGRESS,
                    createdAt: new Date("2025-08-12T13:00:00"),
                }
            ], 
            count: 2 
        },
        dateFromFilter: { 
            rows: [
                {
                    petInfo: {
                        hn: "HN1",
                        name: "Leno",
                        gender: PetGenderTypeEnum.MALE,
                        species: PetSpeciesEnum.DOG,
                        breed: "Labrador",
                    },
                    doctorInfo: {
                        prefix: "Dr.",
                        firstName: "Steve",
                        lastName: "Rogers",
                    },
                    ownerInfo: {
                        firstName: "John",
                        lastName: "Doe",
                        phoneNumber: "0912345678",
                        email: "<EMAIL>",
                    },
                    id: validVisitUUID1,
                    petId: validVisitUUID1,
                    doctorId: validVisitUUID1,
                    visitStep: VisitStepEnum.STEP_ONE,
                    status: VisitStatusEnum.WAITING,
                    createdAt: new Date("2025-08-12T12:00:00"),
                },
                {
                    petInfo: {
                        hn: "HN2",
                        name: "Milo",
                        gender: PetGenderTypeEnum.MALE,
                        species: PetSpeciesEnum.DOG,
                        breed: "Golden Retriever",
                    },
                    doctorInfo: {
                        prefix: "Dr.",
                        firstName: "Steve",
                        lastName: "Rogers",
                    },
                    ownerInfo: {
                        firstName: "John",
                        lastName: "Doe",
                        phoneNumber: "0912345678",
                        email: "<EMAIL>",
                    },
                    id: validVisitUUID2,
                    petId: validVisitUUID2,
                    doctorId: validVisitUUID1,
                    visitStep: VisitStepEnum.STEP_TWO,
                    status: VisitStatusEnum.INPROGRESS,
                    createdAt: new Date("2025-08-13T13:00:00"),
                }
            ], 
            count: 2 
        },
        dateToFilter: { 
            rows: [
                {
                    petInfo: {
                        hn: "HN1",
                        name: "Leno",
                        gender: PetGenderTypeEnum.MALE,
                        species: PetSpeciesEnum.DOG,
                        breed: "Labrador",
                    },
                    doctorInfo: {
                        prefix: "Dr.",
                        firstName: "Steve",
                        lastName: "Rogers",
                    },
                    ownerInfo: {
                        firstName: "John",
                        lastName: "Doe",
                        phoneNumber: "0912345678",
                        email: "<EMAIL>",
                    },
                    id: validVisitUUID1,
                    petId: validVisitUUID1,
                    doctorId: validVisitUUID1,
                    visitStep: VisitStepEnum.STEP_ONE,
                    status: VisitStatusEnum.WAITING,
                    createdAt: new Date("2025-08-12T12:00:00"),
                },
                {
                    petInfo: {
                        hn: "HN2",
                        name: "Milo",
                        gender: PetGenderTypeEnum.MALE,
                        species: PetSpeciesEnum.DOG,
                        breed: "Golden Retriever",
                    },
                    doctorInfo: {
                        prefix: "Dr.",
                        firstName: "Steve",
                        lastName: "Rogers",
                    },
                    ownerInfo: {
                        firstName: "John",
                        lastName: "Doe",
                        phoneNumber: "0912345678",
                        email: "<EMAIL>",
                    },
                    id: validVisitUUID2,
                    petId: validVisitUUID2,
                    doctorId: validVisitUUID1,
                    visitStep: VisitStepEnum.STEP_TWO,
                    status: VisitStatusEnum.INPROGRESS,
                    createdAt: new Date("2025-08-13T13:00:00"),
                }
            ], 
            count: 2 
        },
        dateRangeFilter: { 
            rows: [
                {
                    petInfo: {
                        hn: "HN1",
                        name: "Leno",
                        gender: PetGenderTypeEnum.MALE,
                        species: PetSpeciesEnum.DOG,
                        breed: "Labrador",
                    },
                    doctorInfo: {
                        prefix: "Dr.",
                        firstName: "Steve",
                        lastName: "Rogers",
                    },
                    ownerInfo: {
                        firstName: "John",
                        lastName: "Doe",
                        phoneNumber: "0912345678",
                        email: "<EMAIL>",
                    },
                    id: validVisitUUID1,
                    petId: validVisitUUID1,
                    doctorId: validVisitUUID1,
                    visitStep: VisitStepEnum.STEP_ONE,
                    status: VisitStatusEnum.WAITING,
                    createdAt: new Date("2025-08-12T12:00:00"),
                },
                {
                    petInfo: {
                        hn: "HN2",
                        name: "Milo",
                        gender: PetGenderTypeEnum.MALE,
                        species: PetSpeciesEnum.DOG,
                        breed: "Golden Retriever",
                    },
                    doctorInfo: {
                        prefix: "Dr.",
                        firstName: "Steve",
                        lastName: "Rogers",
                    },
                    ownerInfo: {
                        firstName: "John",
                        lastName: "Doe",
                        phoneNumber: "0912345678",
                        email: "<EMAIL>",
                    },
                    id: validVisitUUID2,
                    petId: validVisitUUID2,
                    doctorId: validVisitUUID1,
                    visitStep: VisitStepEnum.STEP_TWO,
                    status: VisitStatusEnum.INPROGRESS,
                    createdAt: new Date("2025-08-12T13:00:00"),
                }
            ], 
            count: 2 
        },
        empty: { rows: [], count: 0 },
    },
    error: {
        invalidLimit: {},
        invalidPage: {},
        invalidPetHnId: {},
        invalidPetName: {},
        invalidOwnerName: {},
        invalidDoctorId: {},
        invalidDateFrom: {},
        invalidDateTo: {},
        invalidDateRange: {},
    },
};
