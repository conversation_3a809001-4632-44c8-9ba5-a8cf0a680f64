import { DiseaseRepository } from "../../repositories/diseases.repository";
import { FormatValidationError, DefaultError, QueryError } from "../../middlewares/errorHandler";
import { ErrorTypeEnum as e } from "../../enum/errors";
import { ResponseMessageEnum as m, ResponseStatusCodeEnum as s } from "../../enum/response";
import { DiseaseEntity, DiseaseAttributes } from "../../entities/diseases.entity";
import { DiseaseRequest } from "./dto/request";

export class DiseaseModule {
    private readonly diseaseRepository;
    constructor() {
        this.diseaseRepository = new DiseaseRepository();
    }
    public async getAll(): Promise<DiseaseAttributes[]> {
        try {
            const diseases = await this.diseaseRepository.getAll();
            const response = diseases.map((disease) => {
                const diseaseEntity = new DiseaseEntity(disease.diseaseId, disease);
                return diseaseEntity.getAttributes();
            });
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ALL_DISEASES_FAILED);
        }
    }
    public async create(data: DiseaseRequest): Promise<void> {
        try {
            const diseaseEntity = new DiseaseEntity(null, null);
            diseaseEntity.setDiseaseName(data.name);
            const diseaseAttributes = diseaseEntity.getAttributes();

            const existingByName = await this.diseaseRepository.findByName(diseaseAttributes.name);
            if (existingByName)
                throw new DefaultError(
                    e.DATABASE_ERROR,
                    m.DUPLICATED_DATA,
                    null,
                    { name: m.DUPLICATED_NAME },
                    s.CONFLICT
                );

            await this.diseaseRepository.create(diseaseAttributes);
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof DefaultError) {
                throw error;
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.CREATE_DISEASE_FAILED);
        }
    }
    public async update(id: string, data: DiseaseRequest): Promise<void> {
        try {
            const diseaseEntity = new DiseaseEntity(id, null);
            diseaseEntity.setDiseaseName(data.name);
            const { diseaseId, name } = diseaseEntity.getAttributes();
            const existingByName = await this.diseaseRepository.findByName(name);
            if (existingByName && existingByName.diseaseId !== diseaseId)
                throw new DefaultError(
                    e.DATABASE_ERROR,
                    m.DUPLICATED_DATA,
                    null,
                    { name: m.DUPLICATED_NAME },
                    s.CONFLICT
                );
            await this.diseaseRepository.update(diseaseId, { name });
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof DefaultError) {
                throw error;
            }
            if (error instanceof QueryError) {
                throw new DefaultError(e.DATABASE_ERROR, error.message, null, error.errors, s.NOT_FOUND);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.UPDATE_DISEASE_FAILED);
        }
    }
    public async delete(id: string): Promise<void> {
        try {
            const validateId = new DiseaseEntity(id, null);
            const validId = validateId.getId();
            await this.diseaseRepository.delete(validId);
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof QueryError) {
                throw new DefaultError(e.DATABASE_ERROR, error.message, null, error.errors, s.NOT_FOUND);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.DELETE_DISEASE_FAILED);
        }
    }
}
