import { OwnerResponse } from "../../owners/dto/response";
import { PetInfo } from "../../../entities/pets.entity";

export type PetResponse = {
    id?: string;
    ownerId: string;
    petInfo: PetInfo;
    isDeleted?: boolean;
    deletedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
};

export type PetWithOwnerResponse = {
    pet: PetResponse;
    owner: OwnerResponse;
};

export type PetListResponse = {
    rows: PetResponse[];
    count: number;
};

export type PetResolveError = {
    petInfo: { hn?: string };
};
