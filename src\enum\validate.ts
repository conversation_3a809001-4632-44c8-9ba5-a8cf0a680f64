export enum ResponseValidationTypeEnum {
    UUID = "Uuid",
    STRING = "String",
    NUMBER = "Number",
    BOOLEAN = "Boolean",
    EMAIL = "Email",
    PHONE = "Phone",
    NO_SPECIAL_CHARACTERS = "NoSpecialCharacters",
    ENUM = "Enum",
    REQUIRED = "Required",
    LETTER_ONLY = "LetterOnly",
    ALL_CHARS = "AllChars",
    NO_PROHIBITED_CHARACTERS = "NoProhibitedCharacters",
    KEYCLOAK_PROHIBITED_CHARACTERS = "KeycloakProhibitedCharacters",
    DATE = "Date",
    RECIPE_DETAILS = "RecipeDetails",
    QUESTIONARE = "Questionare",
    NUTRIENT_RECIPE = "NutrientRecipe",
    NUTRIENT = "Nutrient",
    PERSONAL_REQUIREMENTS = "PersonalRequirements",
    EXTRA_MEALS = "ExtraMeals",
    PET_INFO = "PetInfo",
    OWNER_INFO = "OwnerInfo",
    DOCTOR_INFO = "DoctorInfo",
    PET_HEALTH = "PetHealth",
    PET_REQUIREMENT = "PetRequirement",
    PET_NUTRITION = "PetNutrition",
    PET_PRODUCT_MATCH = "PetProductMatch",
    WEIGHTS = "Weights",
    OBJECT = "Object",
    PREFIX = "Prefix",
    ARRAY = "Array",
}

export enum ResponseValidationMessageEnum {
    UUID = "InvalidUuid",
    STRING = "InvalidString",
    NUMBER = "InvalidNumber",
    BOOLEAN = "InvalidBoolean",
    EMAIL = "InvalidEmail",
    PHONE = "InvalidPhone",
    NO_SPECIAL_CHARACTERS = "InvalidNoSpecialCharacters",
    ENUM = "InvalidEnum",
    ARRAY = "InvalidArray",
    OBJECT = "InvalidObject",
    REQUIRED = "Required",
    LETTER_ONLY = "InvalidLetterOnly",
    ALL_CHARS = "InvalidAllChars",
    MAX_CHAR = "InvalidMaximumCharacters",
    MIN_CHAR = "InvalidMinimumCharacters",
    MIN_NUMBER = "InvalidMinimumNumber",
    MAX_NUMBER = "InvalidMaximumNumber",
    MIN_ARRAY_ELEMENT = "InvalidMinimumArrayElement",
    MAX_ARRAY_ELEMENT = "InvalidMaximumArrayElement",
    NO_PROHIBITED_CHARACTERS = "InvalidNoProhibitedCharacters",
    DATE = "InvalidDateFormat",
    RECIPE_DETAILS = "InvalidRecipeDetails",
    QUESTIONARE = "InvalidQuestionare",
    NUTRIENT_RECIPE = "InvalidNutrientRecipe",
    NUTRIENT = "InvalidNutrient",
    PERSONAL_REQUIREMENTS = "InvalidPersonalRequirements",
    EXTRA_MEALS = "InvalidExtraMeals",
    PET_INFO = "InvalidPetInfo",
    OWNER_INFO = "InvalidOwnerInfo",
    DOCTOR_INFO = "InvalidDoctorInfo",
    PET_HEALTH = "InvalidPetHealth",
    PET_REQUIREMENT = "InvalidPetRequirement",
    PET_NUTRITION = "InvalidPetNutrition",
    PET_PRODUCT_MATCH = "InvalidPetProductMatch",
    WEIGHTS = "InvalidWeights",
    PREFIX = "InvalidPrefix",
}

export enum ResponseValidationDuplicate {
    DUPLICATED_EMAIL = "DuplicatedEmail",
    DUPLICATED_PHONE = "DuplicatedPhone",
}
