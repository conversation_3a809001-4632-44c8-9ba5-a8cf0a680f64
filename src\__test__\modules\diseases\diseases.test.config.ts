import { TestCase } from "../../utils/testRunner";
import { DiseaseModule } from "../../../modules/diseases/disease.module";
import { DiseaseRequest } from "../../../modules/diseases/dto/request";
import { getAllMocks, createMocks, updateMocks, deleteMocks } from "./diseases.test.mock";
import { DefaultError, QueryError } from "../../../middlewares/errorHandler";
import { DiseaseAttributes } from "../../../entities/diseases.entity";
import { DatabaseErrorEnum as d } from "../../../enum/errors";
import { ResponseMessageEnum as m } from "../../../enum/response";

type MethodTypeMapper = {
    getAll: { input: void; output: DiseaseAttributes[] };
    create: { input: { data: DiseaseRequest }; output: void };
    update: { input: { id: string; data: DiseaseRequest }; output: void };
    delete: { input: { id: string }; output: void };
};

type ModuleTestConfig = {
    name: string;
    testCases: {
        [K in keyof DiseaseModule]?: {
            cases: TestCase<MethodTypeMapper[K]["input"], MethodTypeMapper[K]["output"]>[];
        };
    };
};

export const mockService = {
    getAll: jest.fn(),
    create: jest.fn(),
    findByName: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
};

jest.mock("../../../repositories/diseases.repository", () => ({
    DiseaseRepository: jest.fn(() => mockService),
}));

const moduleInstance = new DiseaseModule();

export const moduleTestConfig: ModuleTestConfig = {
    name: "Diseases",
    testCases: {
        getAll: {
            cases: [
                {
                    name: "[SUCCESS] should return all diseases",
                    setup: {
                        input: undefined,
                        mocks: () => {
                            mockService.getAll.mockResolvedValue(getAllMocks.serviceResponse.success.data);
                        },
                    },
                    method: async () => {
                        return await moduleInstance.getAll();
                    },
                    expectation: {
                        result: getAllMocks.response.success.data,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getAll,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty array when no diseases found",
                    setup: {
                        input: undefined,
                        mocks: () => {
                            mockService.getAll.mockResolvedValue(getAllMocks.serviceResponse.empty.data);
                        },
                    },
                    method: async () => {
                        return await moduleInstance.getAll();
                    },
                    expectation: {
                        result: getAllMocks.response.empty.data,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getAll,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw repository error when unexpected error occurs",
                    setup: {
                        input: undefined,
                        mocks: () => {
                            mockService.getAll.mockRejectedValue(new Error("Unexpected error"));
                        },
                    },
                    method: async () => {
                        return await moduleInstance.getAll();
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getAll,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw repository error when missing key error occurs",
                    setup: {
                        input: undefined,
                        mocks: () => {
                            mockService.getAll.mockResolvedValue(getAllMocks.serviceResponse.missingKey.data);
                        },
                    },
                    method: async () => {
                        return await moduleInstance.getAll();
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.missingKey,
                        },
                        methodCalls: [
                            {
                                method: mockService.getAll,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw repository error when invalid value error occurs",
                    setup: {
                        input: undefined,
                        mocks: () => {
                            mockService.getAll.mockResolvedValue(getAllMocks.serviceResponse.invalidValue.data);
                        },
                    },
                    method: async () => {
                        return await moduleInstance.getAll();
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.invalidValue,
                        },
                        methodCalls: [
                            {
                                method: mockService.getAll,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        create: {
            cases: [
                {
                    name: "[SUCCESS] should create a new disease",
                    setup: {
                        input: {
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.findByName.mockResolvedValue(createMocks.serviceResponse.empty.data);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: createMocks.response.success.data,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.findByName,
                                called: true,
                            },
                            {
                                method: mockService.create,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when missing required fields",
                    setup: {
                        input: {
                            data: createMocks.request.missingBodyKey as unknown as DiseaseRequest,
                        },
                        mocks: () => {},
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.missingBodyRequiredKey,
                        },
                        methodCalls: [
                            {
                                method: mockService.findByName,
                                called: false,
                            },
                            {
                                method: mockService.create,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when missing fields",
                    setup: {
                        input: {
                            data: createMocks.request.emptyBody as unknown as DiseaseRequest,
                        },
                        mocks: () => {},
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.missingBodyRequiredKey,
                        },
                        methodCalls: [
                            {
                                method: mockService.findByName,
                                called: false,
                            },
                            {
                                method: mockService.create,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when invalid value",
                    setup: {
                        input: {
                            data: createMocks.request.invalidBodyValue,
                        },
                        mocks: () => {},
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.invalidBodyValue,
                        },
                        methodCalls: [
                            {
                                method: mockService.findByName,
                                called: false,
                            },
                            {
                                method: mockService.create,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when invalid min length",
                    setup: {
                        input: {
                            data: createMocks.request.invalidBodyMinLength,
                        },
                        mocks: () => {},
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.invalidBodyMinLength,
                        },
                        methodCalls: [
                            {
                                method: mockService.findByName,
                                called: false,
                            },
                            {
                                method: mockService.create,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when invalid max length",
                    setup: {
                        input: {
                            data: createMocks.request.invalidBodyMaxLength,
                        },
                        mocks: () => {},
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.invalidBodyMaxLength,
                        },
                        methodCalls: [
                            {
                                method: mockService.findByName,
                                called: false,
                            },
                            {
                                method: mockService.create,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw default error when unexpected error occurs",
                    setup: {
                        input: {
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.findByName.mockResolvedValue(createMocks.serviceResponse.empty.data);
                            mockService.create.mockRejectedValue(new Error("Unexpected error"));
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.findByName,
                                called: true,
                            },
                            {
                                method: mockService.create,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw repository error when duplicate name error occurs",
                    setup: {
                        input: {
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.findByName.mockImplementationOnce(
                                () => createMocks.serviceResponse.duplicateName.data
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.duplicateName,
                        },
                        methodCalls: [
                            {
                                method: mockService.findByName,
                                called: true,
                            },
                            {
                                method: mockService.create,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        update: {
            cases: [
                {
                    name: "[SUCCESS] should update a disease",
                    setup: {
                        input: {
                            id: updateMocks.request.validId.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.findByName.mockResolvedValue(updateMocks.serviceResponse.empty.data);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.id, input.data);
                    },
                    expectation: {
                        result: updateMocks.response.success.data,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.findByName,
                                called: true,
                            },
                            {
                                method: mockService.update,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when invalid uuid",
                    setup: {
                        input: {
                            id: updateMocks.request.invalidId.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {},
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.invalidId,
                        },
                        methodCalls: [
                            {
                                method: mockService.findByName,
                                called: false,
                            },
                            {
                                method: mockService.update,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when missing required fields",
                    setup: {
                        input: {
                            id: updateMocks.request.validId.id,
                            data: updateMocks.request.missingBodyKey as unknown as DiseaseRequest,
                        },
                        mocks: () => {},
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.missingBodyRequiredKey,
                        },
                        methodCalls: [
                            {
                                method: mockService.findByName,
                                called: false,
                            },
                            {
                                method: mockService.update,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when missing fields",
                    setup: {
                        input: {
                            id: updateMocks.request.validId.id,
                            data: updateMocks.request.emptyBody as unknown as DiseaseRequest,
                        },
                        mocks: () => {},
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.missingBodyRequiredKey,
                        },
                        methodCalls: [
                            {
                                method: mockService.findByName,
                                called: false,
                            },
                            {
                                method: mockService.update,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when invalid value",
                    setup: {
                        input: {
                            id: updateMocks.request.validId.id,
                            data: updateMocks.request.invalidBodyValue,
                        },
                        mocks: () => {},
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.invalidBodyValue,
                        },
                        methodCalls: [
                            {
                                method: mockService.findByName,
                                called: false,
                            },
                            {
                                method: mockService.update,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when invalid min length",
                    setup: {
                        input: {
                            id: updateMocks.request.validId.id,
                            data: updateMocks.request.invalidBodyMinLength,
                        },
                        mocks: () => {},
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.invalidBodyMinLength,
                        },
                        methodCalls: [
                            {
                                method: mockService.findByName,
                                called: false,
                            },
                            {
                                method: mockService.update,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when invalid max length",
                    setup: {
                        input: {
                            id: updateMocks.request.validId.id,
                            data: updateMocks.request.invalidBodyMaxLength,
                        },
                        mocks: () => {},
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.invalidBodyMaxLength,
                        },
                        methodCalls: [
                            {
                                method: mockService.findByName,
                                called: false,
                            },
                            {
                                method: mockService.update,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw default error when unexpected error occurs",
                    setup: {
                        input: {
                            id: updateMocks.request.validId.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.findByName.mockResolvedValue(updateMocks.serviceResponse.empty.data);
                            mockService.update.mockRejectedValue(new Error("Unexpected error"));
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.findByName,
                                called: true,
                            },
                            {
                                method: mockService.update,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw repository error when duplicate name error occurs",
                    setup: {
                        input: {
                            id: updateMocks.request.validId.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.findByName.mockImplementationOnce(
                                () => updateMocks.serviceResponse.duplicateName.data
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.duplicateName,
                        },
                        methodCalls: [
                            {
                                method: mockService.findByName,
                                called: true,
                            },
                            {
                                method: mockService.update,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw database error when disease not found",
                    setup: {
                        input: {
                            id: updateMocks.request.validId.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.update.mockRejectedValue(new QueryError(m.NOT_FOUND, d.RECORD_NOT_FOUND));
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.notFound,
                        },
                        methodCalls: [
                            {
                                method: mockService.update,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        delete: {
            cases: [
                {
                    name: "[SUCCESS] should delete a disease",
                    setup: {
                        input: {
                            id: deleteMocks.request.validId.id,
                        },
                        mocks: () => {},
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.id);
                    },
                    expectation: {
                        result: deleteMocks.response.success.data,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.delete,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when invalid id",
                    setup: {
                        input: {
                            id: deleteMocks.request.invalidId.id,
                        },
                        mocks: () => {},
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.invalidId,
                        },
                        methodCalls: [
                            {
                                method: mockService.delete,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw repository error when unexpected error occurs",
                    setup: {
                        input: {
                            id: deleteMocks.request.validId.id,
                        },
                        mocks: () => {
                            mockService.delete.mockRejectedValue(new Error("Unexpected error"));
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.delete,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw database error when disease not found",
                    setup: {
                        input: {
                            id: deleteMocks.request.validId.id,
                        },
                        mocks: () => {
                            mockService.delete.mockRejectedValue(new QueryError(m.NOT_FOUND, d.RECORD_NOT_FOUND));
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.notFound,
                        },
                        methodCalls: [
                            {
                                method: mockService.delete,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
    },
};
