import {
    VisitRequest,
    CreateVisitFromPetRequest,
    PetInfoSubmitRequest,
    PetHealthSubmitRequest,
    PetRequirementSubmitRequest,
    PetNutritionSubmitRequest,
    PetProductMatchSubmitRequest,
    OrderSummarySubmitRequest,
    GetVisitListFilterRequest,
} from "../../../modules/visits/dto/request";
import { VisitResponse, MapNutrientsResponse, VisitWithOwnerListResponse } from "../../../modules/visits/dto/response";
import { TestCase } from "../../utils/testRunner";
import { VisitModule } from "../../../modules/visits/visits.module";
import { createFromPetMocks, getListMocks } from "./visits.test.mock";
import { DefaultError } from "../../../middlewares/errorHandler";

type MethodTypeMapper = {
    create: { input: { data: VisitRequest }; output: VisitResponse };
    createFromPet: { input: { data: CreateVisitFromPetRequest }; output: void };
    handlePetInfoStep: { input: PetInfoSubmitRequest; output: boolean };
    handlePetHealthStep: { input: PetHealthSubmitRequest; output: boolean };
    handlePetRequirementStep: { input: PetRequirementSubmitRequest; output: boolean };
    handlePetNutritionStep: { input: PetNutritionSubmitRequest; output: boolean };
    handlePetProductMatchStep: { input: PetProductMatchSubmitRequest; output: boolean };
    handleOrderSummaryStep: { input: OrderSummarySubmitRequest; output: boolean };
    getPersonalizedNutrients: { input: string; output: MapNutrientsResponse };
    getOne: { input: string; output: VisitResponse | null };
    getAll: { input: void; output: VisitResponse[] };
    getList: { input: { limit: number; page: number; filter?: GetVisitListFilterRequest }; output: VisitWithOwnerListResponse };
    update: { input: { id: string; data: VisitRequest }; output: boolean };
    delete: { input: string; output: boolean };
};

type ModuleTestConfig = {
    name: string;
    testCases: {
        [K in keyof VisitModule]?: {
            cases: TestCase<MethodTypeMapper[K]["input"], MethodTypeMapper[K]["output"]>[];
        };
    };
};

const mockVisitRepository = {
    create: jest.fn(),
    findList: jest.fn(),
};

jest.mock("../../../repositories/visits.repository", () => ({
    VisitRepository: jest.fn(() => mockVisitRepository),
}));

// Mock pet module
const mockPetModule = {
    createWithOwner: jest.fn(),
};

jest.mock("../../../modules/pets/pets.module", () => ({
    PetModule: jest.fn(() => mockPetModule),
}));

const moduleInstance = new VisitModule();

export const moduleTestConfig: ModuleTestConfig = {
    name: "Visits",
    testCases: {
        createFromPet: {
            cases: [
                {
                    name: "[SUCCESS] should create a new visit with new pet and new owner",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.validBody.validWithNewOwner,
                        },
                        mocks: () => {
                            mockVisitRepository.create.mockResolvedValue(createFromPetMocks.response.success);
                            mockPetModule.createWithOwner.mockResolvedValue(
                                createFromPetMocks.petModuleResponse.success
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: createFromPetMocks.response.success,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: true,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should create a new visit with new pet and existing owner",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.validBody.validWithExistOwner,
                        },
                        mocks: () => {
                            mockVisitRepository.create.mockResolvedValue(createFromPetMocks.response.success);
                            mockPetModule.createWithOwner.mockResolvedValue(
                                createFromPetMocks.petModuleResponse.success
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: createFromPetMocks.response.success,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: true,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when owner info is invalid",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.invalidBody
                                .invalidOwnerInfo as unknown as CreateVisitFromPetRequest,
                        },
                        mocks: () => {
                            mockPetModule.createWithOwner.mockRejectedValue(
                                createFromPetMocks.petModuleResponse.error.invalidOwnerInfo
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createFromPetMocks.error.invalidOwnerInfo,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: false,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when pet info is invalid",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.invalidBody
                                .invalidPetInfo as unknown as CreateVisitFromPetRequest,
                        },
                        mocks: () => {
                            mockPetModule.createWithOwner.mockRejectedValue(
                                createFromPetMocks.petModuleResponse.error.invalidPetInfo
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createFromPetMocks.error.invalidPetInfo,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: false,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when doctor info is invalid",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.invalidBody
                                .invalidDoctorInfo as unknown as CreateVisitFromPetRequest,
                        },
                        mocks: () => {
                            mockPetModule.createWithOwner.mockResolvedValue(
                                createFromPetMocks.petModuleResponse.success
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createFromPetMocks.error.invalidDoctorInfo,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: false,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when visit attributes are invalid",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.invalidBody
                                .invalidVisitAttributes as unknown as CreateVisitFromPetRequest,
                        },
                        mocks: () => {
                            mockPetModule.createWithOwner.mockResolvedValue(
                                createFromPetMocks.petModuleResponse.success
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createFromPetMocks.error.invalidVisitAttributes,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: false,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when pet hn already exist",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.invalidBody.existingPetHN as CreateVisitFromPetRequest,
                        },
                        mocks: () => {
                            mockPetModule.createWithOwner.mockRejectedValue(
                                createFromPetMocks.petModuleResponse.error.petHNAlreadyExist
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createFromPetMocks.error.petHNAlreadyExist,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: false,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when owner phone number already exist",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.invalidBody.existingOwnerPhoneNumber as CreateVisitFromPetRequest,
                        },
                        mocks: () => {
                            mockPetModule.createWithOwner.mockRejectedValue(
                                createFromPetMocks.petModuleResponse.error.ownerPhoneNumberAlreadyExist
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createFromPetMocks.error.ownerPhoneNumberAlreadyExist,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: false,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when not define owner id and owner info",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.invalidBody.notDefineOwnerIdandOwnerInfo as CreateVisitFromPetRequest,
                        },
                        mocks: () => {
                            mockPetModule.createWithOwner.mockRejectedValue(
                                createFromPetMocks.petModuleResponse.error.notDefineOwnerIdandOwnerInfo
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createFromPetMocks.error.notDefineOwnerIdandOwnerInfo,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: false,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when define both owner id and owner info",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.invalidBody.defineBothOwnerIdandOwnerInfo as CreateVisitFromPetRequest,
                        },
                        mocks: () => {
                            mockPetModule.createWithOwner.mockRejectedValue(
                                createFromPetMocks.petModuleResponse.error.defineBothOwnerIdandOwnerInfo
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createFromPetMocks.error.defineBothOwnerIdandOwnerInfo,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: false,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when owner info is empty",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.invalidBody.emptyOwnerInfo as CreateVisitFromPetRequest,
                        },
                        mocks: () => {
                            mockPetModule.createWithOwner.mockRejectedValue(
                                createFromPetMocks.petModuleResponse.error.emptyOwnerInfo
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createFromPetMocks.error.emptyOwnerInfo,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: false,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when pet info is empty",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.invalidBody.emptyPetInfo as CreateVisitFromPetRequest,
                        },
                        mocks: () => {
                            mockPetModule.createWithOwner.mockRejectedValue(
                                createFromPetMocks.petModuleResponse.error.emptyPetInfo
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createFromPetMocks.error.emptyPetInfo,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: false,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw unexpected error from pet module",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.validBody.validWithNewOwner,
                        },
                        mocks: () => {
                            mockPetModule.createWithOwner.mockRejectedValue(
                                createFromPetMocks.petModuleResponse.error.unexpectedError
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createFromPetMocks.error.unexpectedErrorFromPetModule,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: false,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw unexpected error from visit module",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.validBody.validWithNewOwner,
                        },
                        mocks: () => {
                            mockPetModule.createWithOwner.mockResolvedValue(
                                createFromPetMocks.petModuleResponse.success
                            );
                            mockVisitRepository.create.mockRejectedValue(
                                createFromPetMocks.error.unexpectedErrorFromVisitModule
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createFromPetMocks.error.unexpectedErrorFromVisitModule,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: true,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        getList: {
            cases: [
                {
                    name: "[SUCCESS] should return list of visits with default filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.defaultFilter.limit,
                            page: getListMocks.request.validParams.defaultFilter.page,
                        },
                        mocks: () => {
                            mockVisitRepository.findList.mockResolvedValue(getListMocks.response.defaultFilter);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.defaultFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.defaultFilter.limit,
                                    getListMocks.request.validParams.defaultFilter.page,
                                    undefined
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list of visits with default filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.defaultFilter.limit,
                            page: getListMocks.request.validParams.defaultFilter.page,
                        },
                        mocks: () => {
                            mockVisitRepository.findList.mockResolvedValue(getListMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.defaultFilter.limit,
                                    getListMocks.request.validParams.defaultFilter.page,
                                    undefined
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                // {
                //     name: "[SUCCESS] should return list of visits with pet hn id filter",
                //     setup: {
                //         input: {
                //             filter: getListMocks.request.validParams.petHnIdFilter,
                //         },
                //         mocks: () => {
                //             mockVisitRepository.findList.mockResolvedValue(getListMocks.response.petHnIdFilter);
                //         },
                //     },
                //     method: async (input) => {
                //         return await moduleInstance.getList(input.filter);
                //     },
                //     expectation: {
                //         result: getListMocks.response.petHnIdFilter,
                //         error: null,
                //         methodCalls: [
                //             {
                //                 method: mockVisitRepository.findList,
                //                 called: true,
                //                 withArgs: [expect.objectContaining(getListMocks.request.validParams.petHnIdFilter)],
                //             },
                //         ],
                //         additionalChecks: null,
                //     },
                // },
                // {
                //     name: "[SUCCESS] should return empty list of visits with pet hn id filter",
                //     setup: {
                //         input: {
                //             filter: getListMocks.request.validParams.petHnIdFilter,
                //         },
                //         mocks: () => {
                //             mockVisitRepository.findList.mockResolvedValue(getListMocks.response.empty);
                //         },
                //     },
                //     method: async (input) => {
                //         return await moduleInstance.getList(input.filter);
                //     },
                //     expectation: {
                //         result: getListMocks.response.empty,
                //         error: null,
                //         methodCalls: [
                //             {
                //                 method: mockVisitRepository.findList,
                //                 called: true,
                //                 withArgs: [expect.objectContaining(getListMocks.request.validParams.petHnIdFilter)],
                //             },
                //         ],
                //         additionalChecks: null,
                //     },
                // },
                // {
                //     name: "[SUCCESS] should return list of visits with pet name filter",
                //     setup: {
                //         input: {
                //             filter: getListMocks.request.validParams.petNameFilter,
                //         },
                //         mocks: () => {
                //             mockVisitRepository.findList.mockResolvedValue(getListMocks.response.petNameFilter);
                //         },
                //     },
                //     method: async (input) => {
                //         return await moduleInstance.getList(input.filter);
                //     },
                //     expectation: {
                //         result: getListMocks.response.petNameFilter,
                //         error: null,
                //         methodCalls: [
                //             {
                //                 method: mockVisitRepository.findList,
                //                 called: true,
                //                 withArgs: [expect.objectContaining(getListMocks.request.validParams.petNameFilter)],
                //             },
                //         ],
                //         additionalChecks: null,
                //     },
                // },
                // {
                //     name: "[SUCCESS] should return empty list of visits with pet name filter",
                //     setup: {
                //         input: {
                //             filter: getListMocks.request.validParams.petNameFilter,
                //         },
                //         mocks: () => {
                //             mockVisitRepository.findList.mockResolvedValue(getListMocks.response.empty);
                //         },
                //     },
                //     method: async (input) => {
                //         return await moduleInstance.getList(input.filter);
                //     },
                //     expectation: {
                //         result: getListMocks.response.empty,
                //         error: null,
                //         methodCalls: [
                //             {
                //                 method: mockVisitRepository.findList,
                //                 called: true,
                //                 withArgs: [expect.objectContaining(getListMocks.request.validParams.petNameFilter)],
                //             },
                //         ],
                //         additionalChecks: null,
                //     },
                // },
                // {
                //     name: "[SUCCESS] should return list of visits with owner firstname filter",
                //     setup: {
                //         input: {
                //             filter: getListMocks.request.validParams.ownerFirstNameFilter,
                //         },
                //         mocks: () => {
                //             mockVisitRepository.findList.mockResolvedValue(getListMocks.response.ownerFirstNameFilter);
                //         },
                //     },
                //     method: async (input) => {
                //         return await moduleInstance.getList(input.filter);
                //     },
                //     expectation: {
                //         result: getListMocks.response.ownerFirstNameFilter,
                //         error: null,
                //         methodCalls: [
                //             {
                //                 method: mockVisitRepository.findList,
                //                 called: true,
                //                 withArgs: [
                //                     expect.objectContaining(getListMocks.request.validParams.ownerFirstNameFilter),
                //                 ],
                //             },
                //         ],
                //         additionalChecks: null,
                //     },
                // },
                // {
                //     name: "[SUCCESS] should return empty list of visits with owner firstname filter",
                //     setup: {
                //         input: {
                //             filter: getListMocks.request.validParams.ownerFirstNameFilter,
                //         },
                //         mocks: () => {
                //             mockVisitRepository.findList.mockResolvedValue(getListMocks.response.empty);
                //         },
                //     },
                //     method: async (input) => {
                //         return await moduleInstance.getList(input.filter);
                //     },
                //     expectation: {
                //         result: getListMocks.response.empty,
                //         error: null,
                //         methodCalls: [
                //             {
                //                 method: mockVisitRepository.findList,
                //                 called: true,
                //                 withArgs: [
                //                     expect.objectContaining(getListMocks.request.validParams.ownerFirstNameFilter),
                //                 ],
                //             },
                //         ],
                //         additionalChecks: null,
                //     },
                // },
                // {
                //     name: "[SUCCESS] should return list of visits with owner lastname filter",
                //     setup: {
                //         input: {
                //             filter: getListMocks.request.validParams.ownerLastNameFilter,
                //         },
                //         mocks: () => {
                //             mockVisitRepository.findList.mockResolvedValue(getListMocks.response.ownerLastNameFilter);
                //         },
                //     },
                //     method: async (input) => {
                //         return await moduleInstance.getList(input.filter);
                //     },
                //     expectation: {
                //         result: getListMocks.response.ownerLastNameFilter,
                //         error: null,
                //         methodCalls: [
                //             {
                //                 method: mockVisitRepository.findList,
                //                 called: true,
                //                 withArgs: [
                //                     expect.objectContaining(getListMocks.request.validParams.ownerLastNameFilter),
                //                 ],
                //             },
                //         ],
                //         additionalChecks: null,
                //     },
                // },
                // {
                //     name: "[SUCCESS] should return empty list of visits with owner lastname filter",
                //     setup: {
                //         input: {
                //             filter: getListMocks.request.validParams.ownerLastNameFilter,
                //         },
                //         mocks: () => {
                //             mockVisitRepository.findList.mockResolvedValue(getListMocks.response.empty);
                //         },
                //     },
                //     method: async (input) => {
                //         return await moduleInstance.getList(input.filter);
                //     },
                //     expectation: {
                //         result: getListMocks.response.empty,
                //         error: null,
                //         methodCalls: [
                //             {
                //                 method: mockVisitRepository.findList,
                //                 called: true,
                //                 withArgs: [
                //                     expect.objectContaining(getListMocks.request.validParams.ownerLastNameFilter),
                //                 ],
                //             },
                //         ],
                //         additionalChecks: null,
                //     },
                // },
                // {
                //     name: "[SUCCESS] should return list of visits with owner fullname filter",
                //     setup: {
                //         input: {
                //             filter: getListMocks.request.validParams.ownerFullNameFilter,
                //         },
                //         mocks: () => {
                //             mockVisitRepository.findList.mockResolvedValue(getListMocks.response.ownerFullNameFilter);
                //         },
                //     },
                //     method: async (input) => {
                //         return await moduleInstance.getList(input.filter);
                //     },
                //     expectation: {
                //         result: getListMocks.response.ownerFullNameFilter,
                //         error: null,
                //         methodCalls: [
                //             {
                //                 method: mockVisitRepository.findList,
                //                 called: true,
                //                 withArgs: [
                //                     expect.objectContaining(getListMocks.request.validParams.ownerFullNameFilter),
                //                 ],
                //             },
                //         ],
                //         additionalChecks: null,
                //     },
                // },
                // {
                //     name: "[SUCCESS] should return empty list of visits with owner fullname filter",
                //     setup: {
                //         input: {
                //             filter: getListMocks.request.validParams.ownerFullNameFilter,
                //         },
                //         mocks: () => {
                //             mockVisitRepository.findList.mockResolvedValue(getListMocks.response.empty);
                //         },
                //     },
                //     method: async (input) => {
                //         return await moduleInstance.getList(input.filter);
                //     },
                //     expectation: {
                //         result: getListMocks.response.empty,
                //         error: null,
                //         methodCalls: [
                //             {
                //                 method: mockVisitRepository.findList,
                //                 called: true,
                //                 withArgs: [
                //                     expect.objectContaining(getListMocks.request.validParams.ownerFullNameFilter),
                //                 ],
                //             },
                //         ],
                //         additionalChecks: null,
                //     },
                // },
                // {
                //     name: "[SUCCESS] should return list of visits with doctor id filter",
                //     setup: {
                //         input: {
                //             filter: getListMocks.request.validParams.doctorIdFilter,
                //         },
                //         mocks: () => {
                //             mockVisitRepository.findList.mockResolvedValue(getListMocks.response.doctorIdFilter);
                //         },
                //     },
                //     method: async (input) => {
                //         return await moduleInstance.getList(input.filter);
                //     },
                //     expectation: {
                //         result: getListMocks.response.doctorIdFilter,
                //         error: null,
                //         methodCalls: [
                //             {
                //                 method: mockVisitRepository.findList,
                //                 called: true,
                //                 withArgs: [expect.objectContaining(getListMocks.request.validParams.doctorIdFilter)],
                //             },
                //         ],
                //         additionalChecks: null,
                //     },
                // },
                // {
                //     name: "[SUCCESS] should return empty list of visits with doctor id filter",
                //     setup: {
                //         input: {
                //             filter: getListMocks.request.validParams.doctorIdFilter,
                //         },
                //         mocks: () => {
                //             mockVisitRepository.findList.mockResolvedValue(getListMocks.response.empty);
                //         },
                //     },
                //     method: async (input) => {
                //         return await moduleInstance.getList(input.filter);
                //     },
                //     expectation: {
                //         result: getListMocks.response.empty,
                //         error: null,
                //         methodCalls: [
                //             {
                //                 method: mockVisitRepository.findList,
                //                 called: true,
                //                 withArgs: [expect.objectContaining(getListMocks.request.validParams.doctorIdFilter)],
                //             },
                //         ],
                //         additionalChecks: null,
                //     },
                // },
                // {
                //     name: "[SUCCESS] should return list of visits with date from filter",
                //     setup: {
                //         input: {
                //             filter: getListMocks.request.validParams.dateFromFilter,
                //         },
                //         mocks: () => {
                //             mockVisitRepository.findList.mockResolvedValue(getListMocks.response.dateFromFilter);
                //         },
                //     },
                //     method: async (input) => {
                //         return await moduleInstance.getList(input.filter);
                //     },
                //     expectation: {
                //         result: getListMocks.response.dateFromFilter,
                //         error: null,
                //         methodCalls: [
                //             {
                //                 method: mockVisitRepository.findList,
                //                 called: true,
                //                 withArgs: [expect.objectContaining(getListMocks.request.validParams.dateFromFilter)],
                //             },
                //         ],
                //         additionalChecks: null,
                //     },
                // },
                // {
                //     name: "[SUCCESS] should return empty list of visits with date from filter",
                //     setup: {
                //         input: {
                //             filter: getListMocks.request.validParams.dateFromFilter,
                //         },
                //         mocks: () => {
                //             mockVisitRepository.findList.mockResolvedValue(getListMocks.response.empty);
                //         },
                //     },
                //     method: async (input) => {
                //         return await moduleInstance.getList(input.filter);
                //     },
                //     expectation: {
                //         result: getListMocks.response.empty,
                //         error: null,
                //         methodCalls: [
                //             {
                //                 method: mockVisitRepository.findList,
                //                 called: true,
                //                 withArgs: [expect.objectContaining(getListMocks.request.validParams.dateFromFilter)],
                //             },
                //         ],
                //         additionalChecks: null,
                //     },
                // },
                // {
                //     name: "[SUCCESS] should return list of visits with date to filter",
                //     setup: {
                //         input: {
                //             filter: getListMocks.request.validParams.dateToFilter,
                //         },
                //         mocks: () => {
                //             mockVisitRepository.findList.mockResolvedValue(getListMocks.response.dateToFilter);
                //         },
                //     },
                //     method: async (input) => {
                //         return await moduleInstance.getList(input.filter);
                //     },
                //     expectation: {
                //         result: getListMocks.response.dateToFilter,
                //         error: null,
                //         methodCalls: [
                //             {
                //                 method: mockVisitRepository.findList,
                //                 called: true,
                //                 withArgs: [expect.objectContaining(getListMocks.request.validParams.dateToFilter)],
                //             },
                //         ],
                //         additionalChecks: null,
                //     },
                // },
                // {
                //     name: "[SUCCESS] should return empty list of visits with date to filter",
                //     setup: {
                //         input: {
                //             filter: getListMocks.request.validParams.dateToFilter,
                //         },
                //         mocks: () => {
                //             mockVisitRepository.findList.mockResolvedValue(getListMocks.response.empty);
                //         },
                //     },
                //     method: async (input) => {
                //         return await moduleInstance.getList(input.filter);
                //     },
                //     expectation: {
                //         result: getListMocks.response.empty,
                //         error: null,
                //         methodCalls: [
                //             {
                //                 method: mockVisitRepository.findList,
                //                 called: true,
                //                 withArgs: [expect.objectContaining(getListMocks.request.validParams.dateToFilter)],
                //             },
                //         ],
                //         additionalChecks: null,
                //     },
                // },
                // {
                //     name: "[SUCCESS] should return list of visits with date range filter",
                //     setup: {
                //         input: {
                //             filter: getListMocks.request.validParams.dateRangeFilter,
                //         },
                //         mocks: () => {
                //             mockVisitRepository.findList.mockResolvedValue(getListMocks.response.dateRangeFilter);
                //         },
                //     },
                //     method: async (input) => {
                //         return await moduleInstance.getList(input.filter);
                //     },
                //     expectation: {
                //         result: getListMocks.response.dateRangeFilter,
                //         error: null,
                //         methodCalls: [
                //             {
                //                 method: mockVisitRepository.findList,
                //                 called: true,
                //                 withArgs: [expect.objectContaining(getListMocks.request.validParams.dateRangeFilter)],
                //             },
                //         ],
                //         additionalChecks: null,
                //     },
                // },
                // {
                //     name: "[SUCCESS] should return empty list of visits with date range filter",
                //     setup: {
                //         input: {
                //             filter: getListMocks.request.validParams.dateRangeFilter,
                //         },
                //         mocks: () => {
                //             mockVisitRepository.findList.mockResolvedValue(getListMocks.response.empty);
                //         },
                //     },
                //     method: async (input) => {
                //         return await moduleInstance.getList(input.filter);
                //     },
                //     expectation: {
                //         result: getListMocks.response.empty,
                //         error: null,
                //         methodCalls: [
                //             {
                //                 method: mockVisitRepository.findList,
                //                 called: true,
                //                 withArgs: [expect.objectContaining(getListMocks.request.validParams.dateRangeFilter)],
                //             },
                //         ],
                //         additionalChecks: null,
                //     },
                // },
            ],
        },
    },
};
