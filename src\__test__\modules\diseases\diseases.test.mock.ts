import { DiseaseAttributes } from "../../../entities/diseases.entity";
import { DiseaseRequest } from "../../../modules/diseases/dto/request";
import { v7 as uuidv7 } from "uuid";
import { ErrorTypeEnum as e, DatabaseErrorEnum as d } from "../../../enum/errors";
import { ResponseMessageEnum as m, ResponseStatusCodeEnum as s } from "../../../enum/response";
import { ResponseValidationMessageEnum as v } from "../../../enum/validate";

const ValidDiseaseBody: DiseaseRequest = {
    name: "Disease",
};
const InvalidDiseaseBody: Record<string, DiseaseRequest> = {
    minLength: { name: "" },
    maxLength: {
        name: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
    },
    invalidValue: { name: "$*@" },
    invalidKey: { somethingElse: "value" } as any as DiseaseRequest,
};
const ValidDiseaseAttributes: DiseaseAttributes = {
    diseaseId: uuidv7(),
    name: "Disease",
    createdAt: new Date(),
    updatedAt: new Date(),
    isDeleted: false,
    deletedAt: null,
};
const ValidDiseaseAttributesDuplicate: DiseaseAttributes = {
    diseaseId: uuidv7(),
    name: "Disease",
    createdAt: new Date(),
    updatedAt: new Date(),
    isDeleted: false,
    deletedAt: null,
};
const InvalidDiseaseAttributes = {
    empty: {},
    invalidValue: {
        diseaseId: "invalid",
        name: "$*",
        createdAt: "invalid",
        updatedAt: "invalid",
        isDeleted: "invalid",
        deletedAt: "invalid",
    },
    invalidKey: {
        somethingElse: "value",
    } as any as DiseaseAttributes,
};
export const getAllMocks = {
    serviceResponse: {
        success: {
            data: [ValidDiseaseAttributes],
        },
        empty: {
            data: [],
        },
        missingKey: {
            data: [InvalidDiseaseAttributes.invalidKey],
        },
        invalidValue: {
            data: [InvalidDiseaseAttributes.invalidValue],
        },
    },
    response: {
        success: {
            data: [ValidDiseaseAttributes],
        },
        empty: {
            data: [],
        },
    },
    error: {
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.GET_ALL_DISEASES_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        missingKey: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                diseaseId: v.REQUIRED,
                name: v.REQUIRED,
                createdAt: v.REQUIRED,
                updatedAt: v.REQUIRED,
                isDeleted: v.REQUIRED,
                deletedAt: v.REQUIRED,
            },
        },
        invalidValue: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                diseaseId: v.UUID,
                name: v.NO_PROHIBITED_CHARACTERS,
                createdAt: v.DATE,
                updatedAt: v.DATE,
                isDeleted: v.BOOLEAN,
                deletedAt: v.DATE,
            },
        },
    },
};
export const createMocks = {
    request: {
        validBody: {
            ...ValidDiseaseBody,
        },
        invalidBodyValue: {
            ...InvalidDiseaseBody.invalidValue,
        },
        missingBodyKey: {
            ...InvalidDiseaseBody.invalidKey,
        },
        emptyBody: {},
        invalidBodyMinLength: {
            ...InvalidDiseaseBody.minLength,
        },
        invalidBodyMaxLength: {
            ...InvalidDiseaseBody.maxLength,
        },
    },
    serviceResponse: {
        success: {
            data: void 0,
        },
        empty: {
            data: null,
        },
        duplicateName: {
            data: { ...ValidDiseaseAttributesDuplicate },
        },
    },
    response: {
        success: {
            data: undefined,
        },
    },
    error: {
        invalidBodyValue: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: v.NO_PROHIBITED_CHARACTERS,
            },
        },
        invalidBodyMinLength: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: { _error: v.MIN_CHAR },
            },
        },
        invalidBodyMaxLength: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: { _error: v.MAX_CHAR },
            },
        },
        missingBodyRequiredKey: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: v.REQUIRED,
            },
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.CREATE_DISEASE_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        duplicateName: {
            errorType: e.DATABASE_ERROR,
            message: m.DUPLICATED_DATA,
            data: null,
            error: {
                name: m.DUPLICATED_NAME,
            },
            statusCode: s.CONFLICT,
        },
    },
};
export const updateMocks = {
    request: {
        validId: {
            id: ValidDiseaseAttributes.diseaseId,
        },
        invalidId: {
            id: "invalidId",
        },
        validBody: {
            ...ValidDiseaseBody,
        },
        invalidBodyValue: {
            ...InvalidDiseaseBody.invalidValue,
        },
        missingBodyKey: {
            ...InvalidDiseaseBody.invalidKey,
        },
        emptyBody: {},
        invalidBodyMinLength: {
            ...InvalidDiseaseBody.minLength,
        },
        invalidBodyMaxLength: {
            ...InvalidDiseaseBody.maxLength,
        },
    },
    serviceResponse: {
        success: {
            data: void 0,
        },
        empty: {
            data: null,
        },
        duplicateName: {
            data: { ...ValidDiseaseAttributesDuplicate },
        },
    },
    response: {
        success: {
            data: undefined,
        },
    },
    error: {
        invalidId: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.UUID,
            },
        },
        invalidBodyValue: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: v.NO_PROHIBITED_CHARACTERS,
            },
        },
        invalidBodyMinLength: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: { _error: v.MIN_CHAR },
            },
        },
        invalidBodyMaxLength: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: { _error: v.MAX_CHAR },
            },
        },
        missingBodyRequiredKey: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: v.REQUIRED,
            },
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.UPDATE_DISEASE_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        duplicateName: {
            errorType: e.DATABASE_ERROR,
            message: m.DUPLICATED_DATA,
            data: null,
            error: {
                name: m.DUPLICATED_NAME,
            },
            statusCode: s.CONFLICT,
        },
        notFound: {
            errorType: e.DATABASE_ERROR,
            message: m.NOT_FOUND,
            data: null,
            error: d.RECORD_NOT_FOUND,
        },
    },
};
export const deleteMocks = {
    request: {
        validId: {
            id: ValidDiseaseAttributes.diseaseId,
        },
        invalidId: {
            id: "invalidId",
        },
    },
    response: {
        success: {
            data: undefined,
        },
    },
    error: {
        invalidId: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.UUID,
            },
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.DELETE_DISEASE_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        notFound: {
            errorType: e.DATABASE_ERROR,
            message: m.NOT_FOUND,
            data: null,
            error: d.RECORD_NOT_FOUND,
        },
    },
};
