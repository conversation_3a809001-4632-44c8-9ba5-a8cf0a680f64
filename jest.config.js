module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  collectCoverage: true, // Enable coverage collection
  collectCoverageFrom: [
    // 'src/modules/doctors/*.ts',
    // 'src/modules/diseases/*.ts',
    // 'src/modules/permissions/*.ts',
    // 'src/modules/roles/*.ts',
    // 'src/modules/users/*.ts',
    'src/modules/visits/*.ts',
    // 'src/modules/diseases/*.ts',
    // 'src/modules/**/*.ts', // Specify which files to collect coverage from
    // 'src/entities/**/*.ts',
    // '!src/**/*.d.ts', // Exclude type declaration files
    // '!src/**/*.spec.ts', // Exclude test files from coverage
    // '!src/**/*.test.ts',
    // '!src/**/index.ts', // Exclude index files 
    // '!src/application/server.ts', // Exclude server.ts
    // '!src/application/app.ts', // Exclude app.ts
  ],
  coverageDirectory: 'coverage', // Directory to output coverage reports
  coverageReporters: ['json', 'lcov', 'text', 'clover'], // Coverage reporters
  testMatch: [
    '**/__tests__/**/*.[jt]s?(x)',
    '**/?(*.)+(spec|test).[tj]s?(x)'
  ], // Specify the pattern for test files
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'], // Ensures Jest recognizes TypeScript files
  roots: ['<rootDir>/src'], // Adjust root folders to include your tests and source code
  moduleDirectories: ['node_modules', '<rootDir>/src'], // Allows absolute imports from src folder
  testPathIgnorePatterns: ['<rootDir>/node_modules/'], // Ignore node_modules explicitly
  globals: {
    'ts-jest': {
      tsconfig: '<rootDir>/tsconfig.json', // Use TypeScript config
    },
  },
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 100,
      statements: 80,
    },
  },
};